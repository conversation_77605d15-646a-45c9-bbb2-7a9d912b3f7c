"""
REPORTING MODULE - GÉNÉRATION DE RAPPORTS
=========================================

Module contenant les fonctionnalités de génération de rapports
pour l'analyseur Lupasco refactorisé.

Modules disponibles :
- report_generator : Générateur de rapports principal
- formatters : Formatage des données pour les rapports (à venir)
- exporters : Export vers différents formats (à venir)

"""

from .report_generator import ReportGenerator

__version__ = "2.0.0"

__all__ = [
    'ReportGenerator'
]

# Imports (seront ajoutés progressivement)
# from .report_generator import ReportGenerator
# from .formatters import *
# from .exporters import *

__all__ = [
    # Sera complété au fur et à mesure
]
