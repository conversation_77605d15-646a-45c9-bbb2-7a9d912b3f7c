RAPPORT D'ANALYSE STATISTIQUE DES SÉQUENCES LUPASCO
============================================================

Date d'analyse : 2025-06-20 23:34:07
Fichier source : C:\Users\<USER>\Desktop\Lupasco5\dataset_baccarat_lupasco_20250617_232800.json
Nombre de parties : 100,000


==================== ANALYSE TRANSITIONS_DESYNC_SYNC ====================

Taille de la séquence : 6,630,025 éléments
Entropie de Shannon : 1.0000 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------
Aucune analyse des runs disponible

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.046934

==================== ANALYSE CYCLES_PERIODE_2_ET_3 ====================

Taille de la séquence : 6,630,026 éléments
Entropie de Shannon : 1.0000 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------
Aucune analyse des runs disponible

AUTOCORRÉLATION :
---------------
   Lag 1 : -0.044130

==================== ANALYSE PREDICTIBILITE_INDEX3_PAR_INDEX1 ====================

Taille de la séquence : 6,630,026 éléments
Entropie de Shannon : 1.3579 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------
Aucune analyse des runs disponible

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.041468

==================== ANALYSE PREDICTIBILITE_INDEX5_PAR_INDEX1 ====================

Taille de la séquence : 6,630,026 éléments
Entropie de Shannon : 3.9307 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------
Aucune analyse des runs disponible

AUTOCORRÉLATION :
---------------
   Lag 1 : -0.047413

==================== ANALYSE LUPASCO_PAR_PARTIES ====================

Taille de la séquence : 6,630,026 éléments
Entropie de Shannon : 3.9307 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------
Aucune analyse des runs disponible

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.076648

==================== ANALYSE INDEX1 ====================

Taille de la séquence : 6,730,025 éléments
Entropie de Shannon : 1.0966 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------

🎯 DESYNC :
   Nombre de runs : 1,045,331
   Longueur moyenne : 3.19
   Longueur médiane : 2.00
   Longueur max : 38
   Écart-type : 2.63
   Probabilité : 0.5029
   Longueur moyenne théorique : 1.99
   Écart longueur moyenne : 1.20
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 327,576 fois
     Longueur 2 : 224,656 fois
     Longueur 3 : 154,235 fois
     Longueur 4 : 106,153 fois
     Longueur 5 : 72,823 fois
     Longueur 6 : 50,474 fois
     Longueur 7 : 34,348 fois
     Longueur 8 : 23,685 fois
     Longueur 9 : 16,259 fois
     Longueur 10 : 11,261 fois
     Longueur 11 : 7,434 fois
     Longueur 12 : 5,180 fois
     Longueur 13 : 3,575 fois
     Longueur 14 : 2,464 fois
     Longueur 15 : 1,674 fois
     Longueur 16 : 1,184 fois
     Longueur 17 : 754 fois
     Longueur 18 : 467 fois
     Longueur 19 : 344 fois
     Longueur 20 : 229 fois
     Longueur 21 : 180 fois
     Longueur 22 : 112 fois
     Longueur 23 : 81 fois
     Longueur 24 : 64 fois
     Longueur 25 : 41 fois
     Longueur 26 : 28 fois
     Longueur 27 : 15 fois
     Longueur 28 : 11 fois
     Longueur 29 : 6 fois
     Longueur 30 : 6 fois
     Longueur 31 : 2 fois
     Longueur 32 : 3 fois
     Longueur 33 : 4 fois
     Longueur 36 : 2 fois
     Longueur 38 : 1 fois

🎯 SYNC :
   Nombre de runs : 1,034,042
   Longueur moyenne : 3.19
   Longueur médiane : 2.00
   Longueur max : 41
   Écart-type : 2.63
   Probabilité : 0.4971
   Longueur moyenne théorique : 2.01
   Écart longueur moyenne : 1.18
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 323,514 fois
     Longueur 2 : 223,245 fois
     Longueur 3 : 152,894 fois
     Longueur 4 : 105,346 fois
     Longueur 5 : 71,835 fois
     Longueur 6 : 49,331 fois
     Longueur 7 : 33,519 fois
     Longueur 8 : 23,388 fois
     Longueur 9 : 16,110 fois
     Longueur 10 : 11,066 fois
     Longueur 11 : 7,448 fois
     Longueur 12 : 5,169 fois
     Longueur 13 : 3,537 fois
     Longueur 14 : 2,392 fois
     Longueur 15 : 1,685 fois
     Longueur 16 : 1,166 fois
     Longueur 17 : 755 fois
     Longueur 18 : 528 fois
     Longueur 19 : 360 fois
     Longueur 20 : 253 fois
     Longueur 21 : 155 fois
     Longueur 22 : 113 fois
     Longueur 23 : 72 fois
     Longueur 24 : 51 fois
     Longueur 25 : 37 fois
     Longueur 26 : 19 fois
     Longueur 27 : 15 fois
     Longueur 28 : 10 fois
     Longueur 29 : 11 fois
     Longueur 30 : 4 fois
     Longueur 31 : 4 fois
     Longueur 32 : 4 fois
     Longueur 33 : 2 fois
     Longueur 34 : 2 fois
     Longueur 39 : 1 fois
     Longueur 41 : 1 fois

ANALYSE GLOBALE :
--------------------
Nombre total de runs : 2,079,373
Longueur moyenne globale : 3.19
Longueur max globale : 41

RUNS TEST (Test de randomness) :
   Runs observés : 2079373
   Runs attendus : 3314903.14
   Z-score : -959.7106
   P-value : 0.000100
   Significatif : Oui

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.331572
   Lag 2 : 0.127729
   Lag 3 : 0.048537
   Lag 4 : 0.017868
   Lag 5 : 0.005993
   Lag 6 : 0.001189
   Lag 7 : -0.000596
   Lag 8 : -0.001387
   Lag 9 : -0.001611
   Lag 10 : -0.001734

==================== ANALYSE INDEX2 ====================

Taille de la séquence : 6,730,025 éléments
Entropie de Shannon : 1.6662 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------

🎯 pair_4 :
   Nombre de runs : 1,574,976
   Longueur moyenne : 1.60
   Longueur médiane : 1.00
   Longueur max : 14
   Écart-type : 0.97
   Probabilité : 0.3789
   Longueur moyenne théorique : 2.64
   Écart longueur moyenne : -1.04
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 986,664 fois
     Longueur 2 : 369,433 fois
     Longueur 3 : 137,315 fois
     Longueur 4 : 51,201 fois
     Longueur 5 : 19,025 fois
     Longueur 6 : 7,185 fois
     Longueur 7 : 2,568 fois
     Longueur 8 : 996 fois
     Longueur 9 : 363 fois
     Longueur 10 : 133 fois
     Longueur 11 : 56 fois
     Longueur 12 : 29 fois
     Longueur 13 : 6 fois
     Longueur 14 : 2 fois

🎯 impair_5 :
   Nombre de runs : 1,410,036
   Longueur moyenne : 1.43
   Longueur médiane : 1.00
   Longueur max : 13
   Écart-type : 0.78
   Probabilité : 0.3032
   Longueur moyenne théorique : 3.30
   Écart longueur moyenne : -1.87
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 989,269 fois
     Longueur 2 : 294,781 fois
     Longueur 3 : 88,471 fois
     Longueur 4 : 26,534 fois
     Longueur 5 : 7,684 fois
     Longueur 6 : 2,330 fois
     Longueur 7 : 662 fois
     Longueur 8 : 208 fois
     Longueur 9 : 67 fois
     Longueur 10 : 18 fois
     Longueur 11 : 8 fois
     Longueur 12 : 2 fois
     Longueur 13 : 2 fois

🎯 pair_6 :
   Nombre de runs : 1,448,271
   Longueur moyenne : 1.46
   Longueur médiane : 1.00
   Longueur max : 12
   Écart-type : 0.81
   Probabilité : 0.3179
   Longueur moyenne théorique : 3.15
   Écart longueur moyenne : -1.69
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 995,038 fois
     Longueur 2 : 311,285 fois
     Longueur 3 : 97,626 fois
     Longueur 4 : 30,530 fois
     Longueur 5 : 9,519 fois
     Longueur 6 : 2,885 fois
     Longueur 7 : 920 fois
     Longueur 8 : 327 fois
     Longueur 9 : 94 fois
     Longueur 10 : 30 fois
     Longueur 11 : 15 fois
     Longueur 12 : 2 fois

ANALYSE GLOBALE :
--------------------
Nombre total de runs : 4,433,283
Longueur moyenne globale : 1.50
Longueur max globale : 14

AUTOCORRÉLATION :
---------------
   Lag 1 : -0.001199
   Lag 2 : -0.002020
   Lag 3 : -0.001862
   Lag 4 : -0.001644
   Lag 5 : -0.001818
   Lag 6 : -0.001887
   Lag 7 : -0.001542
   Lag 8 : -0.001279
   Lag 9 : -0.002344
   Lag 10 : -0.001806

==================== ANALYSE INDEX3 ====================

Taille de la séquence : 6,730,025 éléments
Entropie de Shannon : 1.4492 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------

🎯 TIE :
   Nombre de runs : 570,116
   Longueur moyenne : 1.11
   Longueur médiane : 1.00
   Longueur max : 6
   Écart-type : 0.34
   Probabilité : 0.0950
   Longueur moyenne théorique : 10.52
   Écart longueur moyenne : -9.42
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 515,932 fois
     Longueur 2 : 48,979 fois
     Longueur 3 : 4,724 fois
     Longueur 4 : 444 fois
     Longueur 5 : 32 fois
     Longueur 6 : 5 fois

🎯 BANKER :
   Nombre de runs : 1,669,207
   Longueur moyenne : 1.82
   Longueur médiane : 1.00
   Longueur max : 18
   Écart-type : 1.22
   Probabilité : 0.4583
   Longueur moyenne théorique : 2.18
   Écart longueur moyenne : -0.36
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 916,946 fois
     Longueur 2 : 413,258 fois
     Longueur 3 : 186,296 fois
     Longueur 4 : 83,651 fois
     Longueur 5 : 38,012 fois
     Longueur 6 : 17,084 fois
     Longueur 7 : 7,633 fois
     Longueur 8 : 3,494 fois
     Longueur 9 : 1,531 fois
     Longueur 10 : 721 fois
     Longueur 11 : 314 fois
     Longueur 12 : 158 fois
     Longueur 13 : 61 fois
     Longueur 14 : 25 fois
     Longueur 15 : 17 fois
     Longueur 16 : 2 fois
     Longueur 17 : 2 fois
     Longueur 18 : 2 fois

🎯 PLAYER :
   Nombre de runs : 1,660,691
   Longueur moyenne : 1.78
   Longueur médiane : 1.00
   Longueur max : 19
   Écart-type : 1.18
   Probabilité : 0.4466
   Longueur moyenne théorique : 2.24
   Écart longueur moyenne : -0.46
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 930,757 fois
     Longueur 2 : 409,612 fois
     Longueur 3 : 179,730 fois
     Longueur 4 : 79,157 fois
     Longueur 5 : 34,262 fois
     Longueur 6 : 15,332 fois
     Longueur 7 : 6,633 fois
     Longueur 8 : 2,927 fois
     Longueur 9 : 1,275 fois
     Longueur 10 : 568 fois
     Longueur 11 : 249 fois
     Longueur 12 : 111 fois
     Longueur 13 : 42 fois
     Longueur 14 : 16 fois
     Longueur 15 : 13 fois
     Longueur 16 : 3 fois
     Longueur 17 : 2 fois
     Longueur 18 : 1 fois
     Longueur 19 : 1 fois

ANALYSE GLOBALE :
--------------------
Nombre total de runs : 3,900,014
Longueur moyenne globale : 1.70
Longueur max globale : 19

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.000392
   Lag 2 : -0.000115
   Lag 3 : -0.000436
   Lag 4 : -0.000146
   Lag 5 : -0.000232
   Lag 6 : -0.000127
   Lag 7 : 0.000329
   Lag 8 : -0.000031
   Lag 9 : 0.000245
   Lag 10 : -0.000221

==================== ANALYSE INDEX5 ====================

Taille de la séquence : 6,730,025 éléments
Entropie de Shannon : 3.9838 bits

ANALYSE DES RUNS PAR VALEUR :
----------------------------------------

🎯 SYNC_impair_5_BANKER :
   Nombre de runs : 516,286
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0779
   Longueur moyenne théorique : 12.84
   Écart longueur moyenne : -11.84
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 516,286 fois

🎯 DESYNC_impair_5_TIE :
   Nombre de runs : 89,138
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0134
   Longueur moyenne théorique : 74.38
   Écart longueur moyenne : -73.38
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 89,138 fois

🎯 SYNC_pair_4_BANKER :
   Nombre de runs : 470,655
   Longueur moyenne : 1.20
   Longueur médiane : 1.00
   Longueur max : 8
   Écart-type : 0.49
   Probabilité : 0.0854
   Longueur moyenne théorique : 11.71
   Écart longueur moyenne : -10.51
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 391,433 fois
     Longueur 2 : 65,890 fois
     Longueur 3 : 11,042 fois
     Longueur 4 : 1,873 fois
     Longueur 5 : 356 fois
     Longueur 6 : 54 fois
     Longueur 7 : 6 fois
     Longueur 8 : 1 fois

🎯 DESYNC_pair_4_BANKER :
   Nombre de runs : 475,639
   Longueur moyenne : 1.20
   Longueur médiane : 1.00
   Longueur max : 8
   Écart-type : 0.49
   Probabilité : 0.0863
   Longueur moyenne théorique : 11.58
   Écart longueur moyenne : -10.38
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 395,224 fois
     Longueur 2 : 66,885 fois
     Longueur 3 : 11,244 fois
     Longueur 4 : 1,915 fois
     Longueur 5 : 310 fois
     Longueur 6 : 55 fois
     Longueur 7 : 3 fois
     Longueur 8 : 3 fois

🎯 DESYNC_pair_6_BANKER :
   Nombre de runs : 377,938
   Longueur moyenne : 1.15
   Longueur médiane : 1.00
   Longueur max : 6
   Écart-type : 0.41
   Probabilité : 0.0653
   Longueur moyenne théorique : 15.31
   Écart longueur moyenne : -14.16
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 329,876 fois
     Longueur 2 : 41,850 fois
     Longueur 3 : 5,451 fois
     Longueur 4 : 654 fois
     Longueur 5 : 95 fois
     Longueur 6 : 12 fois

🎯 SYNC_pair_6_PLAYER :
   Nombre de runs : 432,982
   Longueur moyenne : 1.18
   Longueur médiane : 1.00
   Longueur max : 7
   Écart-type : 0.46
   Probabilité : 0.0770
   Longueur moyenne théorique : 12.98
   Écart longueur moyenne : -11.80
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 367,018 fois
     Longueur 2 : 55,945 fois
     Longueur 3 : 8,478 fois
     Longueur 4 : 1,313 fois
     Longueur 5 : 203 fois
     Longueur 6 : 19 fois
     Longueur 7 : 6 fois

🎯 SYNC_pair_6_BANKER :
   Nombre de runs : 374,179
   Longueur moyenne : 1.15
   Longueur médiane : 1.00
   Longueur max : 8
   Écart-type : 0.41
   Probabilité : 0.0648
   Longueur moyenne théorique : 15.44
   Écart longueur moyenne : -14.30
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 326,241 fois
     Longueur 2 : 41,661 fois
     Longueur 3 : 5,487 fois
     Longueur 4 : 681 fois
     Longueur 5 : 96 fois
     Longueur 6 : 10 fois
     Longueur 7 : 2 fois
     Longueur 8 : 1 fois

🎯 DESYNC_pair_4_PLAYER :
   Nombre de runs : 475,984
   Longueur moyenne : 1.20
   Longueur médiane : 1.00
   Longueur max : 8
   Écart-type : 0.50
   Probabilité : 0.0864
   Longueur moyenne théorique : 11.57
   Écart longueur moyenne : -10.37
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 395,331 fois
     Longueur 2 : 67,049 fois
     Longueur 3 : 11,298 fois
     Longueur 4 : 1,908 fois
     Longueur 5 : 331 fois
     Longueur 6 : 54 fois
     Longueur 7 : 10 fois
     Longueur 8 : 3 fois

🎯 SYNC_impair_5_TIE :
   Nombre de runs : 87,574
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0132
   Longueur moyenne théorique : 75.71
   Écart longueur moyenne : -74.71
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 87,574 fois

🎯 SYNC_impair_5_PLAYER :
   Nombre de runs : 395,408
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0596
   Longueur moyenne théorique : 16.77
   Écart longueur moyenne : -15.77
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 395,408 fois

🎯 SYNC_pair_4_TIE :
   Nombre de runs : 113,658
   Longueur moyenne : 1.04
   Longueur médiane : 1.00
   Longueur max : 4
   Écart-type : 0.19
   Probabilité : 0.0178
   Longueur moyenne théorique : 56.30
   Écart longueur moyenne : -55.26
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 109,697 fois
     Longueur 2 : 3,820 fois
     Longueur 3 : 137 fois
     Longueur 4 : 4 fois

🎯 SYNC_pair_6_TIE :
   Nombre de runs : 104,419
   Longueur moyenne : 1.03
   Longueur médiane : 1.00
   Longueur max : 4
   Écart-type : 0.19
   Probabilité : 0.0163
   Longueur moyenne théorique : 61.42
   Écart longueur moyenne : -60.39
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 100,999 fois
     Longueur 2 : 3,324 fois
     Longueur 3 : 91 fois
     Longueur 4 : 5 fois

🎯 DESYNC_pair_6_PLAYER :
   Nombre de runs : 438,441
   Longueur moyenne : 1.18
   Longueur médiane : 1.00
   Longueur max : 7
   Écart-type : 0.46
   Probabilité : 0.0780
   Longueur moyenne théorique : 12.81
   Écart longueur moyenne : -11.63
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 371,525 fois
     Longueur 2 : 56,777 fois
     Longueur 3 : 8,526 fois
     Longueur 4 : 1,359 fois
     Longueur 5 : 216 fois
     Longueur 6 : 30 fois
     Longueur 7 : 8 fois

🎯 DESYNC_impair_5_BANKER :
   Nombre de runs : 521,756
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0787
   Longueur moyenne théorique : 12.71
   Écart longueur moyenne : -11.71
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 521,756 fois

🎯 SYNC_pair_4_PLAYER :
   Nombre de runs : 469,369
   Longueur moyenne : 1.20
   Longueur médiane : 1.00
   Longueur max : 8
   Écart-type : 0.49
   Probabilité : 0.0852
   Longueur moyenne théorique : 11.74
   Écart longueur moyenne : -10.54
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 389,990 fois
     Longueur 2 : 66,020 fois
     Longueur 3 : 11,110 fois
     Longueur 4 : 1,870 fois
     Longueur 5 : 312 fois
     Longueur 6 : 59 fois
     Longueur 7 : 7 fois
     Longueur 8 : 1 fois

🎯 DESYNC_pair_6_TIE :
   Nombre de runs : 105,788
   Longueur moyenne : 1.03
   Longueur médiane : 1.00
   Longueur max : 4
   Écart-type : 0.19
   Probabilité : 0.0165
   Longueur moyenne théorique : 60.61
   Écart longueur moyenne : -59.58
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 102,314 fois
     Longueur 2 : 3,352 fois
     Longueur 3 : 120 fois
     Longueur 4 : 2 fois

🎯 DESYNC_pair_4_TIE :
   Nombre de runs : 114,022
   Longueur moyenne : 1.04
   Longueur médiane : 1.00
   Longueur max : 4
   Écart-type : 0.20
   Probabilité : 0.0178
   Longueur moyenne théorique : 56.08
   Écart longueur moyenne : -55.04
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 109,966 fois
     Longueur 2 : 3,914 fois
     Longueur 3 : 136 fois
     Longueur 4 : 6 fois

🎯 DESYNC_impair_5_PLAYER :
   Nombre de runs : 399,837
   Longueur moyenne : 1.00
   Longueur médiane : 1.00
   Longueur max : 1
   Écart-type : 0.00
   Probabilité : 0.0603
   Longueur moyenne théorique : 16.58
   Écart longueur moyenne : -15.58
   Test KS p-value : 0.000100
   Test KS significatif : Oui
   Distribution complète des longueurs :
     Longueur 1 : 399,837 fois

ANALYSE GLOBALE :
--------------------
Nombre total de runs : 5,963,073
Longueur moyenne globale : 1.11
Longueur max globale : 8

AUTOCORRÉLATION :
---------------
   Lag 1 : 0.018118
   Lag 2 : 0.007155
   Lag 3 : 0.003326
   Lag 4 : 0.000811
   Lag 5 : 0.000290
   Lag 6 : -0.000030
   Lag 7 : -0.000448
   Lag 8 : -0.000141
   Lag 9 : -0.000800
   Lag 10 : 0.000192

============================================================
ANALYSE INDEX5 AVEC FORMULES MATHÉMATIQUES EXACTES
============================================================

1. ANALYSE GLOBALE INDEX5
-------------------------
Nombre de combinaisons trouvées : 18/18
Entropie de Shannon globale : 3.930687 bits
Coefficient de Gini global : 0.283523
Coefficient de variation global : 0.541275
Autocorrélation lag 1 : 0.294628
Test des runs p-value : 0.000000
Séquence aléatoire : Non

2. ANALYSE DÉTAILLÉE PAR COMBINAISON
-----------------------------------

🎯 Combinaison  1/18 : DESYNC_impair_5_BANKER
   Occurrences : 521,756 (0.0787)
   Runs p-value : 0.061316
   Aléatoire : Oui
   Autocorr lag 1 : -0.072761
   Entropie locale : 0.288623
   Anomalies détectées : 0
   Z-score max : 0.742

🎯 Combinaison  2/18 : DESYNC_impair_5_PLAYER
   Occurrences : 399,837 (0.0603)
   Runs p-value : 0.219603
   Aléatoire : Oui
   Autocorr lag 1 : 0.066573
   Entropie locale : 0.244336
   Anomalies détectées : 0
   Z-score max : 1.400

🎯 Combinaison  3/18 : DESYNC_impair_5_TIE
   Occurrences : 89,138 (0.0134)
   Runs p-value : 0.925577
   Aléatoire : Oui
   Autocorr lag 1 : -0.008828
   Entropie locale : 0.083583
   Anomalies détectées : 0
   Z-score max : 0.991

🎯 Combinaison  4/18 : DESYNC_pair_4_BANKER
   Occurrences : 572,311 (0.0863)
   Runs p-value : 0.668839
   Aléatoire : Oui
   Autocorr lag 1 : 0.034769
   Entropie locale : 0.305071
   Anomalies détectées : 0
   Z-score max : 1.023

🎯 Combinaison  5/18 : DESYNC_pair_4_PLAYER
   Occurrences : 573,028 (0.0864)
   Runs p-value : 0.555897
   Aléatoire : Oui
   Autocorr lag 1 : 0.009126
   Entropie locale : 0.305297
   Anomalies détectées : 0
   Z-score max : 0.325

🎯 Combinaison  6/18 : DESYNC_pair_4_TIE
   Occurrences : 118,226 (0.0178)
   Runs p-value : 0.893471
   Aléatoire : Oui
   Autocorr lag 1 : 0.064240
   Entropie locale : 0.103593
   Anomalies détectées : 0
   Z-score max : 0.714

🎯 Combinaison  7/18 : DESYNC_pair_6_BANKER
   Occurrences : 433,092 (0.0653)
   Runs p-value : 0.538300
   Aléatoire : Oui
   Autocorr lag 1 : 0.006253
   Entropie locale : 0.257128
   Anomalies détectées : 0
   Z-score max : 0.306

🎯 Combinaison  8/18 : DESYNC_pair_6_PLAYER
   Occurrences : 517,409 (0.0780)
   Runs p-value : 0.866064
   Aléatoire : Oui
   Autocorr lag 1 : 0.067182
   Entropie locale : 0.287160
   Anomalies détectées : 0
   Z-score max : 1.027

🎯 Combinaison  9/18 : DESYNC_pair_6_TIE
   Occurrences : 109,386 (0.0165)
   Runs p-value : 0.636638
   Aléatoire : Oui
   Autocorr lag 1 : 0.022308
   Entropie locale : 0.097697
   Anomalies détectées : 0
   Z-score max : 1.408

🎯 Combinaison 10/18 : SYNC_impair_5_BANKER
   Occurrences : 516,286 (0.0779)
   Runs p-value : 0.072508
   Aléatoire : Oui
   Autocorr lag 1 : -0.069795
   Entropie locale : 0.286781
   Anomalies détectées : 0
   Z-score max : 0.136

🎯 Combinaison 11/18 : SYNC_impair_5_PLAYER
   Occurrences : 395,408 (0.0596)
   Runs p-value : 0.904096
   Aléatoire : Oui
   Autocorr lag 1 : 0.067696
   Entropie locale : 0.242588
   Anomalies détectées : 0
   Z-score max : 1.257

🎯 Combinaison 12/18 : SYNC_impair_5_TIE
   Occurrences : 87,574 (0.0132)
   Runs p-value : 0.243205
   Aléatoire : Oui
   Autocorr lag 1 : -0.052646
   Entropie locale : 0.082454
   Anomalies détectées : 0
   Z-score max : 1.306

🎯 Combinaison 13/18 : SYNC_pair_4_BANKER
   Occurrences : 565,985 (0.0854)
   Runs p-value : 0.411765
   Aléatoire : Oui
   Autocorr lag 1 : -0.014406
   Entropie locale : 0.303068
   Anomalies détectées : 0
   Z-score max : 0.748

🎯 Combinaison 14/18 : SYNC_pair_4_PLAYER
   Occurrences : 564,811 (0.0852)
   Runs p-value : 0.131777
   Aléatoire : Oui
   Autocorr lag 1 : -0.040840
   Entropie locale : 0.302694
   Anomalies détectées : 0
   Z-score max : 0.770

🎯 Combinaison 15/18 : SYNC_pair_4_TIE
   Occurrences : 117,764 (0.0178)
   Runs p-value : 0.668839
   Aléatoire : Oui
   Autocorr lag 1 : 0.034769
   Entropie locale : 0.103288
   Anomalies détectées : 0
   Z-score max : 0.985

🎯 Combinaison 16/18 : SYNC_pair_6_BANKER
   Occurrences : 429,310 (0.0648)
   Runs p-value : 0.044523
   Aléatoire : Non
   Autocorr lag 1 : 0.049692
   Entropie locale : 0.255702
   Anomalies détectées : 0
   Z-score max : 1.299

🎯 Combinaison 17/18 : SYNC_pair_6_PLAYER
   Occurrences : 510,765 (0.0770)
   Runs p-value : 0.893471
   Aléatoire : Oui
   Autocorr lag 1 : 0.066376
   Entropie locale : 0.284909
   Anomalies détectées : 0
   Z-score max : 1.254

🎯 Combinaison 18/18 : SYNC_pair_6_TIE
   Occurrences : 107,940 (0.0163)
   Runs p-value : 0.091850
   Aléatoire : Oui
   Autocorr lag 1 : 0.007790
   Entropie locale : 0.096718
   Anomalies détectées : 0
   Z-score max : 0.158

=================================================================
ANALYSE INDEX2_INDEX3 AVEC FORMULES MATHÉMATIQUES EXACTES
=================================================================

1. ANALYSE GLOBALE INDEX2_INDEX3
------------------------------
Nombre de combinaisons trouvées : 9/9
Entropie de Shannon globale : 2.930713 bits
Coefficient de Gini global : 0.283053
Coefficient de variation global : 0.557888
Autocorrélation lag 1 : -0.000260
Test des runs p-value : 0.465034
Séquence aléatoire : Oui

2. ANALYSE DÉTAILLÉE PAR COMBINAISON INDEX2_INDEX3
---------------------------------------------

🎯 Combinaison  1/9 : impair_5_BANKER
   Occurrences : 1,038,042 (0.1566)
   Runs p-value : 0.434714
   Aléatoire : Oui
   Autocorr lag 1 : -0.043994
   Entropie locale : 0.418840
   Anomalies détectées : 0
   Z-score max : 0.733

🎯 Combinaison  2/9 : impair_5_PLAYER
   Occurrences : 795,245 (0.1199)
   Runs p-value : 0.592621
   Aléatoire : Oui
   Autocorr lag 1 : -0.049067
   Entropie locale : 0.366980
   Anomalies détectées : 0
   Z-score max : 1.362

🎯 Combinaison  3/9 : impair_5_TIE
   Occurrences : 176,712 (0.0267)
   Runs p-value : 0.270223
   Aléatoire : Oui
   Autocorr lag 1 : -0.019528
   Entropie locale : 0.139385
   Anomalies détectées : 0
   Z-score max : 1.218

🎯 Combinaison  4/9 : pair_4_BANKER
   Occurrences : 1,138,296 (0.1717)
   Runs p-value : 0.104414
   Aléatoire : Oui
   Autocorr lag 1 : -0.064585
   Entropie locale : 0.436455
   Anomalies détectées : 0
   Z-score max : 0.976

🎯 Combinaison  5/9 : pair_4_PLAYER
   Occurrences : 1,137,839 (0.1716)
   Runs p-value : 0.563598
   Aléatoire : Oui
   Autocorr lag 1 : 0.010383
   Entropie locale : 0.436379
   Anomalies détectées : 0
   Z-score max : 0.306

🎯 Combinaison  6/9 : pair_4_TIE
   Occurrences : 235,990 (0.0356)
   Runs p-value : 0.743238
   Aléatoire : Oui
   Autocorr lag 1 : 0.039712
   Entropie locale : 0.171287
   Anomalies détectées : 0
   Z-score max : 0.977

=================================================================
ANALYSE INDEX1_INDEX3 AVEC FORMULES MATHÉMATIQUES EXACTES
=================================================================

1. ANALYSE GLOBALE INDEX1_INDEX3
------------------------------
Nombre de combinaisons trouvées : 6/6
Entropie de Shannon globale : 2.357869 bits
Coefficient de Gini global : 0.243166
Coefficient de variation global : 0.554043
Autocorrélation lag 1 : 0.326810
Test des runs p-value : 0.052406
Séquence aléatoire : Oui

2. ANALYSE DÉTAILLÉE PAR COMBINAISON INDEX1_INDEX3
---------------------------------------------

🎯 Combinaison  1/6 : DESYNC_BANKER
   Occurrences : 1,527,159 (0.2303)
   Runs p-value : 0.284768
   Aléatoire : Oui
   Autocorr lag 1 : 0.003503
   Entropie locale : 0.487898
   Anomalies détectées : 0
   Z-score max : 401.596

🎯 Combinaison  2/6 : DESYNC_PLAYER
   Occurrences : 1,490,274 (0.2248)
   Runs p-value : 0.814033
   Aléatoire : Oui
   Autocorr lag 1 : 0.051271
   Entropie locale : 0.484042
   Anomalies détectées : 0
   Z-score max : 366.507

🎯 Combinaison  3/6 : DESYNC_TIE
   Occurrences : 316,750 (0.0478)
   Runs p-value : 0.320723
   Aléatoire : Oui
   Autocorr lag 1 : -0.029270
   Entropie locale : 0.209618
   Anomalies détectées : 0
   Z-score max : 749.867

🎯 Combinaison  4/6 : SYNC_BANKER
   Occurrences : 1,511,581 (0.2280)
   Runs p-value : 0.243205
   Aléatoire : Oui
   Autocorr lag 1 : -0.052646
   Entropie locale : 0.486293
   Anomalies détectées : 0
   Z-score max : 386.777

🎯 Combinaison  5/6 : SYNC_PLAYER
   Occurrences : 1,470,984 (0.2219)
   Runs p-value : 0.095555
   Aléatoire : Oui
   Autocorr lag 1 : -0.066032
   Entropie locale : 0.481947
   Anomalies détectées : 0
   Z-score max : 348.157

🎯 Combinaison  6/6 : SYNC_TIE
   Occurrences : 313,278 (0.0473)
   Runs p-value : 0.284219
   Aléatoire : Oui
   Autocorr lag 1 : -0.035230
   Entropie locale : 0.208071
   Anomalies détectées : 0
   Z-score max : 753.170

======================================================================
VALIDATION DES FORMULES MATHÉMATIQUES
======================================================================

Tests réussis : 8/8
Toutes validées : ✅ OUI

Détail des tests :
   gini_coefficient: ✅ PASS
   shannon_entropy: ✅ PASS
   autocorrelation: ✅ PASS
   coefficient_variation: ✅ PASS
   runs_test: ✅ PASS
   entropy_conditional: ✅ PASS
   mutual_information: ✅ PASS
   joint_entropy: ✅ PASS


======================================================================
ANALYSE DES TRANSITIONS DESYNC→SYNC
======================================================================

Total transitions analysées : 6,630,025
Transitions DESYNC→SYNC : 1,014,667 (15.30%)
Transitions SYNC→DESYNC : 1,014,666 (15.30%)



======================================================================
ANALYSE DES CYCLES PÉRIODE 2 ET 3
======================================================================

Séquence INDEX1 :
   Cycles période 2 : 2404962
   Cycles période 3 : 1289386
   Total cycles détectés : 3694348

Séquence INDEX2 :
   Cycles période 2 : 749832
   Cycles période 3 : 251722
   Total cycles détectés : 1001554

Séquence INDEX3 :
   Cycles période 2 : 1162906
   Cycles période 3 : 486576
   Total cycles détectés : 1649482

Séquence INDEX5 :
   Cycles période 2 : 77613
   Cycles période 3 : 9866
   Total cycles détectés : 87479


======================================================================
PRÉDICTIBILITÉ INDEX3 PAR INDEX1
======================================================================

Score de prédictibilité : 1.000000
Réduction d'incertitude : 0.00%
INDEX3 prédictible : Non
Information mutuelle : 0.000000 bits


======================================================================
PRÉDICTIBILITÉ INDEX5 PAR INDEX1
======================================================================

Score de prédictibilité : 1.000000
Réduction d'incertitude : 0.00%
INDEX5 prédictible : Non
Information mutuelle : 0.000000 bits


======================================================================
ANALYSE LUPASCO PAR PARTIES
======================================================================

Parties analysées : 50
Parties disponibles : 100000
Taux de réussite : 100.0%
Parties valides : 50
Parties avec erreur : 0

Moyennes des métriques Lupasco :
   entropy_index1 : 0.000000
   entropy_index2 : 0.000000
   entropy_index3 : 0.000000
   entropy_joint_12 : 0.000000
   entropy_joint_13 : 0.000000
   entropy_joint_23 : 0.000000
   entropy_joint_123 : 0.000000
   entropy_cond_1_given_2 : 0.000000
   entropy_cond_1_given_3 : 0.000000
   entropy_cond_2_given_1 : 0.000000
   entropy_cond_2_given_3 : 0.000000
   entropy_cond_3_given_1 : 0.000000
   entropy_cond_3_given_2 : 0.000000
   mutual_info_12 : 0.000000
   mutual_info_13 : 0.000000
   mutual_info_23 : 0.000000


