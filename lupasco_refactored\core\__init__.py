"""
CORE MODULE - CLASSES PRINCIPALES
=================================

Module contenant les classes principales et la logique de base
de l'analyseur Lupasco refactorisé.

Classes disponibles :
- AnalyseurSequencesLupasco : Classe principale refactorisée
- DataLoader : Chargement et gestion des données
- SequenceExtractor : Extraction des séquences INDEX

"""

from .data_loader import DataLoader
from .sequence_extractor import SequenceExtractor
from .base_analyzer import AnalyseurSequencesLupasco

__version__ = "2.0.0"

__all__ = [
    'DataLoader',
    'SequenceExtractor',
    'AnalyseurSequencesLupasco'
]
