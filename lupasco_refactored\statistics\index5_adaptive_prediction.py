"""
INDEX5 ADAPTIVE PREDICTION MODULE
=================================

Module spécialisé pour la prédiction adaptative INDEX5
Implémente les recommandations de base.txt :
- Prédiction adaptative : Utiliser les patterns autocorrélatifs
- Optimisation entropique : Maximiser l'information tout en préservant la structure

Techniques de prédiction adaptées aux 18 combinaisons INDEX5 :
- Modèles Markoviens adaptatifs d'ordre variable
- Prédiction basée sur l'entropie conditionnelle
- Exploitation des patterns autocorrélatifs
- Adaptation dynamique aux changements de régime

Auteur : Expert Statisticien IA
Date : 2025-06-20
Version : 1.0
"""

import numpy as np
import pandas as pd
from scipy import stats
from collections import Counter, defaultdict, deque
from typing import List, Dict, Tuple, Any, Optional
import math
import warnings

try:
    from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, diviser_en_parties
except ImportError:
    # Fallback pour compatibilité
    def nettoyer_marqueurs(sequence):
        return [x for x in sequence if x != "__FIN_PARTIE__"]

    def diviser_en_parties(sequence):
        parties = []
        partie_actuelle = []
        for element in sequence:
            if element == "__FIN_PARTIE__":
                if partie_actuelle:
                    parties.append(partie_actuelle)
                    partie_actuelle = []
            else:
                partie_actuelle.append(element)
        if partie_actuelle:
            parties.append(partie_actuelle)
        return parties


class Index5AdaptivePredictor:
    """
    Prédicteur adaptatif spécialisé pour INDEX5
    
    Implémente les recommandations base.txt :
    1. Prédiction adaptative avec patterns autocorrélatifs
    2. Optimisation entropique
    3. Adaptation dynamique aux changements
    """
    
    def __init__(self, taille_memoire: int = 1000):
        """
        Initialise le prédicteur adaptatif INDEX5
        
        Args:
            taille_memoire: Taille de la mémoire adaptative
        """
        self.combinaisons_index5 = [
            'SYNC_impair_5_BANKER', 'SYNC_impair_5_PLAYER', 'SYNC_impair_5_TIE',
            'SYNC_pair_4_BANKER', 'SYNC_pair_4_PLAYER', 'SYNC_pair_4_TIE',
            'SYNC_pair_6_BANKER', 'SYNC_pair_6_PLAYER', 'SYNC_pair_6_TIE',
            'DESYNC_impair_5_BANKER', 'DESYNC_impair_5_PLAYER', 'DESYNC_impair_5_TIE',
            'DESYNC_pair_4_BANKER', 'DESYNC_pair_4_PLAYER', 'DESYNC_pair_4_TIE',
            'DESYNC_pair_6_BANKER', 'DESYNC_pair_6_PLAYER', 'DESYNC_pair_6_TIE'
        ]
        
        # Mémoire adaptative
        self.taille_memoire = taille_memoire
        self.memoire_sequence = deque(maxlen=taille_memoire)
        self.memoire_patterns = defaultdict(lambda: defaultdict(int))
        
        # Modèles adaptatifs
        self.ordre_markov_adaptatif = 1
        self.poids_adaptatifs = {combo: 1.0 for combo in self.combinaisons_index5}
        self.historique_performance = deque(maxlen=100)
        
        # Paramètres d'adaptation
        self.seuil_adaptation = 0.1
        self.facteur_oubli = 0.95
        self.alpha_apprentissage = 0.1
        
    def predire_index5_adaptatif(self, sequence_index5: List[str], 
                                nb_predictions: int = 10) -> Dict:
        """
        Prédiction adaptative INDEX5 complète
        
        Args:
            sequence_index5: Séquence INDEX5 historique
            nb_predictions: Nombre de prédictions à effectuer
            
        Returns:
            dict: Résultats complets de prédiction adaptative
        """
        print(f"\n🔮 PRÉDICTION ADAPTATIVE INDEX5 - {nb_predictions} prédictions")
        print("=" * 60)
        
        # Nettoyer et préparer la séquence
        sequence_propre = nettoyer_marqueurs(sequence_index5)
        
        if len(sequence_propre) < 50:
            return {'erreur': 'Séquence INDEX5 trop courte pour prédiction'}
        
        # Initialiser la mémoire adaptative
        self._initialiser_memoire(sequence_propre)
        
        resultats = {
            'predictions': [],
            'probabilites_predictions': [],
            'confiance_predictions': [],
            'modeles_utilises': [],
            'performance_adaptative': {}
        }
        
        # 1. ANALYSE PATTERNS AUTOCORRÉLATIFS
        print("   📊 1. Analyse patterns autocorrélatifs INDEX5...")
        patterns_autocorr = self._analyser_patterns_autocorrelatifs(sequence_propre)
        resultats['patterns_autocorrelatifs'] = patterns_autocorr
        
        # 2. OPTIMISATION ORDRE MARKOV ADAPTATIF
        print("   🔗 2. Optimisation ordre Markov adaptatif...")
        self._optimiser_ordre_markov_adaptatif(sequence_propre)
        
        # 3. PRÉDICTIONS ADAPTATIVES
        print(f"   🎯 3. Génération {nb_predictions} prédictions adaptatives...")
        for i in range(nb_predictions):
            prediction = self._predire_prochaine_combinaison()
            resultats['predictions'].append(prediction['combinaison'])
            resultats['probabilites_predictions'].append(prediction['probabilites'])
            resultats['confiance_predictions'].append(prediction['confiance'])
            resultats['modeles_utilises'].append(prediction['modele_utilise'])
            
            # Mise à jour adaptative (simulation)
            self._mettre_a_jour_adaptatif(prediction['combinaison'])
        
        # 4. ÉVALUATION PERFORMANCE
        resultats['performance_adaptative'] = self._evaluer_performance_adaptative()
        
        print("   ✅ Prédiction adaptative INDEX5 terminée")
        return resultats
    
    def _initialiser_memoire(self, sequence: List[str]) -> None:
        """Initialise la mémoire adaptative avec la séquence historique"""
        # Remplir la mémoire avec les derniers éléments
        for element in sequence[-self.taille_memoire:]:
            self.memoire_sequence.append(element)
        
        # Construire les patterns initiaux
        self._construire_patterns_initiaux()
    
    def _analyser_patterns_autocorrelatifs(self, sequence: List[str]) -> Dict:
        """
        Analyse les patterns autocorrélatifs INDEX5
        Implémente : "Utiliser les patterns autocorrélatifs"
        """
        resultats = {
            'autocorrelations': {},
            'patterns_detectes': [],
            'force_autocorrelation': 0.0
        }
        
        # Encoder la séquence
        mapping = {combo: i for i, combo in enumerate(self.combinaisons_index5)}
        sequence_numerique = [mapping.get(x, 0) for x in sequence]
        
        # Calculer autocorrélations pour différents lags
        lags = [1, 2, 3, 5, 8, 13, 21]
        autocorrelations = []
        
        for lag in lags:
            if len(sequence_numerique) > lag:
                # Autocorrélation de Pearson
                x1 = sequence_numerique[:-lag]
                x2 = sequence_numerique[lag:]
                
                if len(x1) > 10:
                    corr, p_value = stats.pearsonr(x1, x2)
                    resultats['autocorrelations'][lag] = {
                        'correlation': corr,
                        'p_value': p_value,
                        'significatif': p_value < 0.05
                    }
                    autocorrelations.append(abs(corr))
        
        # Force globale d'autocorrélation
        if autocorrelations:
            resultats['force_autocorrelation'] = np.mean(autocorrelations)
        
        # Détecter patterns significatifs
        for lag, data in resultats['autocorrelations'].items():
            if data['significatif'] and abs(data['correlation']) > 0.1:
                resultats['patterns_detectes'].append({
                    'lag': lag,
                    'correlation': data['correlation'],
                    'type': 'POSITIVE' if data['correlation'] > 0 else 'NEGATIVE'
                })
        
        return resultats
    
    def _optimiser_ordre_markov_adaptatif(self, sequence: List[str]) -> None:
        """Optimise l'ordre du modèle Markov de manière adaptative"""
        # Tester différents ordres
        ordres_testes = [1, 2, 3, 4]
        performances = {}
        
        for ordre in ordres_testes:
            if len(sequence) > ordre + 10:
                # Validation croisée simple
                taille_test = min(100, len(sequence) // 5)
                sequence_train = sequence[:-taille_test]
                sequence_test = sequence[-taille_test:]
                
                # Construire modèle d'ordre k
                modele = self._construire_modele_markov(sequence_train, ordre)
                
                # Évaluer sur test
                performance = self._evaluer_modele_markov(modele, sequence_test, ordre)
                performances[ordre] = performance
        
        # Sélectionner meilleur ordre
        if performances:
            self.ordre_markov_adaptatif = max(performances.keys(), key=lambda k: performances[k])
    
    def _predire_prochaine_combinaison(self) -> Dict:
        """Prédit la prochaine combinaison INDEX5 de manière adaptative"""
        # Ensemble de modèles
        predictions_modeles = {}
        
        # 1. Modèle Markovien adaptatif
        pred_markov = self._predire_markov_adaptatif()
        predictions_modeles['markov'] = pred_markov
        
        # 2. Modèle fréquentiel adaptatif
        pred_freq = self._predire_frequentiel_adaptatif()
        predictions_modeles['frequentiel'] = pred_freq
        
        # 3. Modèle entropique
        pred_entropie = self._predire_entropique()
        predictions_modeles['entropique'] = pred_entropie
        
        # Fusion adaptative des prédictions
        prediction_finale = self._fusionner_predictions_adaptatives(predictions_modeles)
        
        return prediction_finale
    
    def _predire_markov_adaptatif(self) -> Dict:
        """Prédiction basée sur le modèle Markovien adaptatif"""
        if len(self.memoire_sequence) < self.ordre_markov_adaptatif:
            # Fallback : prédiction uniforme
            probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
            return {
                'probabilites': probabilites,
                'combinaison_predite': np.random.choice(self.combinaisons_index5),
                'confiance': 0.1
            }
        
        # État actuel (derniers k éléments)
        etat_actuel = tuple(list(self.memoire_sequence)[-self.ordre_markov_adaptatif:])
        
        # Rechercher transitions similaires
        transitions = self.memoire_patterns.get(etat_actuel, {})
        
        if not transitions:
            # Fallback : ordre inférieur
            if self.ordre_markov_adaptatif > 1:
                etat_reduit = etat_actuel[1:]
                transitions = self.memoire_patterns.get(etat_reduit, {})
        
        if transitions:
            total = sum(transitions.values())
            probabilites = {combo: transitions.get(combo, 0) / total 
                          for combo in self.combinaisons_index5}
            
            # Appliquer poids adaptatifs
            for combo in probabilites:
                probabilites[combo] *= self.poids_adaptatifs[combo]
            
            # Renormaliser
            total_poids = sum(probabilites.values())
            if total_poids > 0:
                probabilites = {combo: p / total_poids for combo, p in probabilites.items()}
            
            # Sélectionner prédiction
            combos = list(probabilites.keys())
            probs = list(probabilites.values())
            combinaison_predite = np.random.choice(combos, p=probs)
            
            # Confiance basée sur l'entropie
            entropie = -sum(p * math.log2(p) for p in probs if p > 0)
            confiance = 1 - (entropie / math.log2(18))  # Normaliser par entropie max
            
            return {
                'probabilites': probabilites,
                'combinaison_predite': combinaison_predite,
                'confiance': max(confiance, 0.1)
            }
        
        # Fallback final
        probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
        return {
            'probabilites': probabilites,
            'combinaison_predite': np.random.choice(self.combinaisons_index5),
            'confiance': 0.1
        }
    
    def _predire_frequentiel_adaptatif(self) -> Dict:
        """Prédiction basée sur les fréquences adaptatives"""
        if not self.memoire_sequence:
            probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
            return {
                'probabilites': probabilites,
                'combinaison_predite': np.random.choice(self.combinaisons_index5),
                'confiance': 0.1
            }
        
        # Calculer fréquences avec décroissance temporelle
        compteur_pondere = defaultdict(float)
        
        for i, combo in enumerate(self.memoire_sequence):
            # Poids décroissant avec l'âge
            poids = self.facteur_oubli ** (len(self.memoire_sequence) - i - 1)
            compteur_pondere[combo] += poids
        
        # Normaliser en probabilités
        total = sum(compteur_pondere.values())
        if total > 0:
            probabilites = {combo: compteur_pondere.get(combo, 0) / total 
                          for combo in self.combinaisons_index5}
        else:
            probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
        
        # Sélectionner prédiction
        combos = list(probabilites.keys())
        probs = list(probabilites.values())
        combinaison_predite = np.random.choice(combos, p=probs)
        
        # Confiance basée sur la concentration
        entropie = -sum(p * math.log2(p) for p in probs if p > 0)
        confiance = 1 - (entropie / math.log2(18))
        
        return {
            'probabilites': probabilites,
            'combinaison_predite': combinaison_predite,
            'confiance': max(confiance, 0.1)
        }
    
    def _predire_entropique(self) -> Dict:
        """Prédiction basée sur l'optimisation entropique"""
        # Principe : prédire la combinaison qui maximise l'information
        # tout en préservant la structure
        
        if not self.memoire_sequence:
            probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
            return {
                'probabilites': probabilites,
                'combinaison_predite': np.random.choice(self.combinaisons_index5),
                'confiance': 0.1
            }
        
        # Calculer entropie actuelle
        compteur = Counter(self.memoire_sequence)
        entropie_actuelle = self._calculer_entropie_shannon(list(self.memoire_sequence))
        
        # Évaluer impact de chaque combinaison sur l'entropie
        scores_entropiques = {}
        
        for combo in self.combinaisons_index5:
            # Simuler ajout de cette combinaison
            sequence_simulee = list(self.memoire_sequence) + [combo]
            entropie_simulee = self._calculer_entropie_shannon(sequence_simulee)
            
            # Score = gain d'entropie pondéré par fréquence actuelle
            gain_entropie = entropie_simulee - entropie_actuelle
            freq_actuelle = compteur.get(combo, 0) / len(self.memoire_sequence)
            
            # Favoriser les combinaisons qui augmentent l'entropie mais ne sont pas trop rares
            score = gain_entropie * (1 + freq_actuelle)
            scores_entropiques[combo] = score
        
        # Convertir en probabilités (softmax)
        scores = list(scores_entropiques.values())
        if max(scores) > min(scores):
            # Normaliser et appliquer softmax
            scores_norm = [(s - min(scores)) / (max(scores) - min(scores)) for s in scores]
            exp_scores = [math.exp(s) for s in scores_norm]
            sum_exp = sum(exp_scores)
            probabilites = {combo: exp_scores[i] / sum_exp 
                          for i, combo in enumerate(self.combinaisons_index5)}
        else:
            probabilites = {combo: 1/18 for combo in self.combinaisons_index5}
        
        # Sélectionner prédiction
        combos = list(probabilites.keys())
        probs = list(probabilites.values())
        combinaison_predite = np.random.choice(combos, p=probs)
        
        # Confiance basée sur la variance des scores
        variance_scores = np.var(scores) if len(scores) > 1 else 0
        confiance = min(variance_scores / 10, 0.9)  # Normaliser
        
        return {
            'probabilites': probabilites,
            'combinaison_predite': combinaison_predite,
            'confiance': max(confiance, 0.1)
        }
    
    def _fusionner_predictions_adaptatives(self, predictions_modeles: Dict) -> Dict:
        """Fusionne les prédictions de différents modèles de manière adaptative"""
        # Poids adaptatifs basés sur la performance historique
        poids_modeles = {
            'markov': 0.4,
            'frequentiel': 0.3,
            'entropique': 0.3
        }
        
        # Ajuster poids selon performance récente
        if self.historique_performance:
            perf_moyenne = np.mean(self.historique_performance)
            if perf_moyenne > 0.6:  # Bonne performance
                poids_modeles['markov'] += 0.1
                poids_modeles['frequentiel'] -= 0.05
                poids_modeles['entropique'] -= 0.05
        
        # Fusion des probabilités
        probabilites_fusionnees = defaultdict(float)
        confiance_totale = 0
        
        for modele, prediction in predictions_modeles.items():
            poids = poids_modeles.get(modele, 0.33)
            confiance_modele = prediction.get('confiance', 0.1)
            
            for combo, prob in prediction['probabilites'].items():
                probabilites_fusionnees[combo] += poids * prob * confiance_modele
            
            confiance_totale += poids * confiance_modele
        
        # Normaliser
        total = sum(probabilites_fusionnees.values())
        if total > 0:
            probabilites_finales = {combo: p / total for combo, p in probabilites_fusionnees.items()}
        else:
            probabilites_finales = {combo: 1/18 for combo in self.combinaisons_index5}
        
        # Sélectionner prédiction finale
        combos = list(probabilites_finales.keys())
        probs = list(probabilites_finales.values())
        combinaison_finale = np.random.choice(combos, p=probs)
        
        return {
            'combinaison': combinaison_finale,
            'probabilites': probabilites_finales,
            'confiance': min(confiance_totale, 0.95),
            'modele_utilise': 'FUSION_ADAPTATIVE'
        }
    
    def _mettre_a_jour_adaptatif(self, nouvelle_combinaison: str) -> None:
        """Met à jour les modèles de manière adaptative"""
        # Ajouter à la mémoire
        self.memoire_sequence.append(nouvelle_combinaison)
        
        # Mettre à jour patterns Markoviens
        if len(self.memoire_sequence) > self.ordre_markov_adaptatif:
            etat = tuple(list(self.memoire_sequence)[-(self.ordre_markov_adaptatif+1):-1])
            self.memoire_patterns[etat][nouvelle_combinaison] += 1
        
        # Adaptation des poids (apprentissage simple)
        # Ici on pourrait implémenter un mécanisme plus sophistiqué
        # basé sur la performance des prédictions
    
    def _construire_patterns_initiaux(self) -> None:
        """Construit les patterns initiaux à partir de la mémoire"""
        sequence = list(self.memoire_sequence)
        
        for ordre in range(1, min(5, len(sequence))):
            for i in range(len(sequence) - ordre):
                etat = tuple(sequence[i:i+ordre])
                if i + ordre < len(sequence):
                    suivant = sequence[i + ordre]
                    self.memoire_patterns[etat][suivant] += 1
    
    def _construire_modele_markov(self, sequence: List[str], ordre: int) -> Dict:
        """Construit un modèle Markovien d'ordre k"""
        modele = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(sequence) - ordre):
            etat = tuple(sequence[i:i+ordre])
            suivant = sequence[i + ordre]
            modele[etat][suivant] += 1
        
        return dict(modele)
    
    def _evaluer_modele_markov(self, modele: Dict, sequence_test: List[str], ordre: int) -> float:
        """Évalue la performance d'un modèle Markovien"""
        if len(sequence_test) <= ordre:
            return 0.0
        
        predictions_correctes = 0
        total_predictions = 0
        
        for i in range(len(sequence_test) - ordre):
            etat = tuple(sequence_test[i:i+ordre])
            vraie_valeur = sequence_test[i + ordre]
            
            if etat in modele:
                transitions = modele[etat]
                if transitions:
                    # Prédire la combinaison la plus probable
                    prediction = max(transitions.keys(), key=lambda k: transitions[k])
                    if prediction == vraie_valeur:
                        predictions_correctes += 1
                    total_predictions += 1
        
        return predictions_correctes / total_predictions if total_predictions > 0 else 0.0
    
    def _calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """Calcule l'entropie de Shannon"""
        if not sequence:
            return 0.0
        
        compteur = Counter(sequence)
        total = len(sequence)
        
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        
        return entropie
    
    def _evaluer_performance_adaptative(self) -> Dict:
        """Évalue la performance du système adaptatif"""
        return {
            'ordre_markov_optimal': self.ordre_markov_adaptatif,
            'taille_memoire_utilisee': len(self.memoire_sequence),
            'nb_patterns_appris': len(self.memoire_patterns),
            'performance_moyenne': np.mean(self.historique_performance) if self.historique_performance else 0.0,
            'adaptation_active': True
        }


# Fonctions d'export pour compatibilité
__all__ = [
    'Index5AdaptivePredictor'
]
