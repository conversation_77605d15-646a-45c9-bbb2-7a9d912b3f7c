"""
EXÉCUTION COMPLÈTE DES MODULES INDEX5
=====================================

Exécution de tous les modules INDEX5 créés sur le fichier JSON principal :
- index5_advanced_entropy.py
- index5_anomaly_detection.py  
- index5_adaptive_prediction.py
- index5_master_analyzer.py

Fichier cible : dataset_test_3_parties_complet.json

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

import sys
import json
import traceback
from datetime import datetime

# Ajouter chemins pour imports
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def charger_donnees_json():
    """Charge les données du fichier JSON principal"""
    fichier_json = "dataset_baccarat_lupasco_20250617_232800.json"
    
    try:
        print(f"📂 Chargement du fichier : {fichier_json}")
        with open(fichier_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Fichier JSON chargé avec succès")
        print(f"📊 Nombre de parties : {len(data.get('parties', []))}")
        
        return data
    except Exception as e:
        print(f"❌ Erreur chargement JSON : {e}")
        return None

def extraire_sequence_index5(data):
    """Extrait la séquence INDEX5 complète"""
    try:
        sequence_index5 = []
        
        for partie in data['parties']:
            for main in partie['mains']:
                if 'index5_combined' in main and main['index5_combined']:
                    sequence_index5.append(main['index5_combined'])
        
        print(f"✅ Séquence INDEX5 extraite : {len(sequence_index5)} éléments")
        
        # Afficher échantillon
        if len(sequence_index5) >= 10:
            print(f"📋 Échantillon : {sequence_index5[:10]}")
        
        return sequence_index5
        
    except Exception as e:
        print(f"❌ Erreur extraction INDEX5 : {e}")
        return []

def executer_module_entropy_avancee(sequence_index5):
    """Exécute le module d'entropie avancée INDEX5"""
    print("\n" + "="*80)
    print("🔬 EXÉCUTION : INDEX5 ADVANCED ENTROPY")
    print("="*80)
    
    try:
        from lupasco_refactored.statistics.index5_advanced_entropy import Index5AdvancedEntropy
        
        analyzer = Index5AdvancedEntropy()
        print(f"📊 Analyse entropique avancée sur {len(sequence_index5)} éléments...")
        
        resultats = analyzer.analyser_index5_complet(sequence_index5)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur : {resultats['erreur']}")
            return None
        
        # Afficher résultats clés
        print("\n📈 RÉSULTATS ENTROPIE AVANCÉE :")
        print("-" * 40)
        
        if 'entropie_multi_echelle' in resultats:
            eme = resultats['entropie_multi_echelle']
            print(f"🔬 Dimension fractale : {eme.get('dimension_fractale', 'N/A'):.4f}")
            print(f"📊 Exposant Hurst : {eme.get('exposant_hurst', 'N/A'):.4f}")
            print(f"🎯 Complexité fractale : {eme.get('complexite_fractale', 'N/A')}")
        
        if 'markov_ordre_superieur' in resultats:
            mos = resultats['markov_ordre_superieur']
            print(f"🔗 Ordre Markov optimal : {mos.get('ordre_optimal', 'N/A')}")
            print(f"🧠 Dépendances cachées : {mos.get('dependances_cachees', 'N/A')}")
        
        if 'analyse_spectrale' in resultats:
            asp = resultats['analyse_spectrale']
            nb_freq = len(asp.get('frequences_dominantes', []))
            print(f"📈 Fréquences dominantes : {nb_freq}")
            print(f"🌊 Entropie spectrale : {asp.get('entropie_spectrale', 'N/A'):.4f}")
        
        if 'tests_stationnarite' in resultats:
            ts = resultats['tests_stationnarite']
            print(f"📏 Stationnarité globale : {ts.get('stationnarite_globale', 'N/A')}")
        
        if 'synthese_index5' in resultats:
            synthese = resultats['synthese_index5']
            print(f"🎯 Score complexité : {synthese.get('score_complexite_globale', 'N/A')}/100")
            print(f"📊 Classification : {synthese.get('classification_index5', 'N/A')}")
            
            reco = synthese.get('recommandations', [])
            print(f"📋 Recommandations : {len(reco)}")
        
        print("✅ Module entropie avancée exécuté avec succès")
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur exécution entropie avancée : {e}")
        traceback.print_exc()
        return None

def executer_module_anomaly_detection(sequence_index5):
    """Exécute le module de détection d'anomalies INDEX5"""
    print("\n" + "="*80)
    print("🚨 EXÉCUTION : INDEX5 ANOMALY DETECTION")
    print("="*80)
    
    try:
        from lupasco_refactored.statistics.index5_anomaly_detection import Index5AnomalyDetector
        
        detector = Index5AnomalyDetector()
        print(f"🔍 Détection anomalies sur {len(sequence_index5)} éléments...")
        
        resultats = detector.detecter_anomalies_index5(sequence_index5)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur : {resultats['erreur']}")
            return None
        
        # Afficher résultats clés
        print("\n🚨 RÉSULTATS DÉTECTION ANOMALIES :")
        print("-" * 40)
        
        print(f"📊 Score anomalie global : {resultats.get('score_anomalie_global', 'N/A'):.2f}/100")
        print(f"⚠️  Classification risque : {resultats.get('classification_risque', 'N/A')}")
        print(f"🔢 Nb anomalies totales : {resultats.get('nb_anomalies_total', 'N/A')}")
        print(f"🚨 Nb anomalies critiques : {resultats.get('nb_anomalies_critiques', 'N/A')}")
        
        # Détail par type d'anomalie
        if 'anomalies_fragmentation' in resultats:
            frag = resultats['anomalies_fragmentation']
            score_frag = frag.get('score_fragmentation', 0)
            print(f"🔧 Score fragmentation : {score_frag:.2f}")
        
        if 'anomalies_frequences' in resultats:
            freq = resultats['anomalies_frequences']
            score_freq = freq.get('score_frequentiel', 0)
            print(f"📈 Score fréquentiel : {score_freq:.2f}")
        
        if 'anomalies_entropie' in resultats:
            entrop = resultats['anomalies_entropie']
            score_entrop = entrop.get('score_entropique', 0)
            print(f"🌊 Score entropique : {score_entrop:.2f}")
        
        # Afficher quelques anomalies
        anomalies = resultats.get('anomalies_detectees', [])
        if anomalies:
            print(f"\n🔍 ANOMALIES DÉTECTÉES (top 5) :")
            for i, anomalie in enumerate(anomalies[:5], 1):
                type_anom = anomalie.get('type', 'N/A')
                severite = anomalie.get('severite', 'N/A')
                print(f"   {i}. {type_anom} - {severite}")
        
        # Recommandations
        reco = resultats.get('recommandations_action', [])
        if reco:
            print(f"\n📋 RECOMMANDATIONS (top 3) :")
            for i, rec in enumerate(reco[:3], 1):
                print(f"   {i}. {rec}")
        
        print("✅ Module détection anomalies exécuté avec succès")
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur exécution détection anomalies : {e}")
        traceback.print_exc()
        return None

def executer_module_adaptive_prediction(sequence_index5):
    """Exécute le module de prédiction adaptative INDEX5"""
    print("\n" + "="*80)
    print("🔮 EXÉCUTION : INDEX5 ADAPTIVE PREDICTION")
    print("="*80)
    
    try:
        from lupasco_refactored.statistics.index5_adaptive_prediction import Index5AdaptivePredictor
        
        predictor = Index5AdaptivePredictor()
        nb_predictions = 10
        print(f"🎯 Prédiction adaptative sur {len(sequence_index5)} éléments...")
        print(f"🔮 Génération de {nb_predictions} prédictions...")
        
        resultats = predictor.predire_index5_adaptatif(sequence_index5, nb_predictions)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur : {resultats['erreur']}")
            return None
        
        # Afficher résultats clés
        print("\n🔮 RÉSULTATS PRÉDICTION ADAPTATIVE :")
        print("-" * 40)
        
        predictions = resultats.get('predictions', [])
        confiances = resultats.get('confiance_predictions', [])
        modeles = resultats.get('modeles_utilises', [])
        
        print(f"🎯 Nb prédictions générées : {len(predictions)}")
        
        if confiances:
            confiance_moy = sum(confiances) / len(confiances)
            print(f"📊 Confiance moyenne : {confiance_moy:.3f}")
        
        # Afficher prédictions
        if predictions:
            print(f"\n🔮 PRÉDICTIONS GÉNÉRÉES :")
            for i, (pred, conf, mod) in enumerate(zip(predictions[:5], confiances[:5], modeles[:5]), 1):
                print(f"   {i}. {pred} (conf: {conf:.3f}, modèle: {mod})")
        
        # Patterns autocorrélatifs
        if 'patterns_autocorrelatifs' in resultats:
            patterns = resultats['patterns_autocorrelatifs']
            force_autocorr = patterns.get('force_autocorrelation', 0)
            print(f"🔗 Force autocorrélation : {force_autocorr:.4f}")
            
            patterns_detectes = patterns.get('patterns_detectes', [])
            print(f"🎯 Patterns détectés : {len(patterns_detectes)}")
        
        # Performance adaptative
        if 'performance_adaptative' in resultats:
            perf = resultats['performance_adaptative']
            print(f"⚙️  Ordre Markov optimal : {perf.get('ordre_markov_optimal', 'N/A')}")
            print(f"🧠 Patterns appris : {perf.get('nb_patterns_appris', 'N/A')}")
            print(f"📈 Performance moyenne : {perf.get('performance_moyenne', 'N/A'):.3f}")
        
        print("✅ Module prédiction adaptative exécuté avec succès")
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur exécution prédiction adaptative : {e}")
        traceback.print_exc()
        return None

def executer_module_master_analyzer(sequence_index5):
    """Exécute le module analyseur maître INDEX5"""
    print("\n" + "="*80)
    print("🎯 EXÉCUTION : INDEX5 MASTER ANALYZER")
    print("="*80)
    
    try:
        from lupasco_refactored.analyzers.index5_master_analyzer import Index5MasterAnalyzer
        
        master = Index5MasterAnalyzer()
        nb_predictions = 8
        print(f"🎯 Analyse maître complète sur {len(sequence_index5)} éléments...")
        print(f"🔮 Avec {nb_predictions} prédictions...")
        
        resultats = master.analyser_index5_complet(
            sequence_index5,
            nb_predictions=nb_predictions,
            generer_rapport=True
        )
        
        if 'erreur' in resultats:
            print(f"❌ Erreur : {resultats['erreur']}")
            return None
        
        # Afficher synthèse globale
        print("\n🎯 SYNTHÈSE GLOBALE MASTER ANALYZER :")
        print("-" * 40)
        
        if 'synthese_globale' in resultats:
            synthese = resultats['synthese_globale']
            
            # Scores globaux
            scores = synthese.get('scores_globaux', {})
            print(f"📊 Score complexité : {scores.get('complexite', 'N/A')}/100")
            print(f"🚨 Score anomalies : {scores.get('anomalies', 'N/A')}/100")
            print(f"🔮 Score prédiction : {scores.get('prediction', 'N/A')}/100")
            print(f"🎯 Score global : {scores.get('global', 'N/A'):.1f}/100")
            
            # Classifications
            print(f"📈 Classification INDEX5 : {synthese.get('classification_index5', 'N/A')}")
            print(f"🔧 Niveau complexité : {synthese.get('niveau_complexite', 'N/A')}")
            print(f"⚠️  Niveau risque : {synthese.get('niveau_risque', 'N/A')}")
            
            # Métriques clés
            metriques = synthese.get('metriques_cles', {})
            print(f"🔢 Nb anomalies : {metriques.get('nb_anomalies_detectees', 'N/A')}")
            print(f"📋 Nb recommandations : {metriques.get('nb_recommandations', 'N/A')}")
        
        # Recommandations prioritaires
        if 'synthese_globale' in resultats:
            reco_prioritaires = resultats['synthese_globale'].get('recommandations_prioritaires', [])
            if reco_prioritaires:
                print(f"\n📋 RECOMMANDATIONS PRIORITAIRES (top 5) :")
                for i, rec in enumerate(reco_prioritaires[:5], 1):
                    print(f"   {i}. {rec}")
        
        # Résumé exécutif
        if 'synthese_globale' in resultats:
            resume = resultats['synthese_globale'].get('resume_executif', '')
            if resume:
                print(f"\n📄 RÉSUMÉ EXÉCUTIF :")
                print("-" * 20)
                print(resume)
        
        # Fichiers générés
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Sauvegarder résultats
        nom_json = f"execution_baccarat_lupasco_{timestamp}.json"
        master.sauvegarder_resultats(resultats, nom_json)

        # Générer rapport
        nom_rapport = f"rapport_baccarat_lupasco_{timestamp}.txt"
        master.generer_rapport_texte(resultats, nom_rapport)
        
        print(f"\n💾 FICHIERS GÉNÉRÉS :")
        print(f"   📄 JSON : {nom_json}")
        print(f"   📋 Rapport : {nom_rapport}")
        
        print("✅ Module master analyzer exécuté avec succès")
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur exécution master analyzer : {e}")
        traceback.print_exc()
        return None

def main():
    """Fonction principale d'exécution"""
    print("🎓 EXÉCUTION COMPLÈTE DES MODULES INDEX5")
    print("="*80)
    print("Expert Statisticien IA - Analyse dataset_baccarat_lupasco_20250617_232800.json")
    print(f"📅 Date : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 1. Charger données JSON
    data = charger_donnees_json()
    if not data:
        print("❌ Impossible de charger les données")
        return False
    
    # 2. Extraire séquence INDEX5
    sequence_index5 = extraire_sequence_index5(data)
    if not sequence_index5:
        print("❌ Impossible d'extraire la séquence INDEX5")
        return False
    
    # Compteur de succès
    modules_reussis = 0
    total_modules = 4
    
    # 3. Exécuter chaque module
    print(f"\n🚀 EXÉCUTION DE {total_modules} MODULES INDEX5...")
    
    # Module 1: Entropie avancée
    if executer_module_entropy_avancee(sequence_index5):
        modules_reussis += 1
    
    # Module 2: Détection anomalies
    if executer_module_anomaly_detection(sequence_index5):
        modules_reussis += 1
    
    # Module 3: Prédiction adaptative
    if executer_module_adaptive_prediction(sequence_index5):
        modules_reussis += 1
    
    # Module 4: Master analyzer
    if executer_module_master_analyzer(sequence_index5):
        modules_reussis += 1
    
    # 4. Résumé final
    print("\n" + "="*80)
    print("📊 RÉSUMÉ EXÉCUTION COMPLÈTE")
    print("="*80)
    print(f"✅ Modules exécutés avec succès : {modules_reussis}/{total_modules}")
    print(f"📈 Taux de réussite : {(modules_reussis/total_modules)*100:.1f}%")
    print(f"📊 Séquence INDEX5 analysée : {len(sequence_index5)} éléments")
    
    if modules_reussis == total_modules:
        print("🎉 EXÉCUTION COMPLÈTE RÉUSSIE !")
        print("✅ Tous les modules INDEX5 fonctionnent parfaitement")
        print("📄 Fichiers de résultats générés automatiquement")
    else:
        print("⚠️  Exécution partielle")
        print(f"🔧 {total_modules - modules_reussis} module(s) à corriger")
    
    print("="*80)
    
    return modules_reussis == total_modules

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
