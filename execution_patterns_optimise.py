"""
EXÉCUTION OPTIMISÉE EXPLOITATION PATTERNS INDEX5 (SANS TIE)
===========================================================

Version optimisée qui utilise pleinement le moteur pattern_exploitation.py
avec ajustements automatiques et diagnostic avancé.

EXCLUSIONS : Les 6 occurrences TIE sont automatiquement exclues de l'analyse :
- SYNC_pair_4_TIE, SYNC_pair_6_TIE, SYNC_impair_5_TIE
- DESYNC_pair_4_TIE, DESYNC_pair_6_TIE, DESYNC_impair_5_TIE

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 2.1 - Optimisée avec exclusion TIE
"""

import sys
import traceback
from datetime import datetime

# Ajouter chemins pour imports
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def charger_donnees_optimise():
    """Chargement optimisé des données avec exclusion des TIE"""
    print("📂 CHARGEMENT DONNÉES OPTIMISÉ")
    print("=" * 50)

    # Définir les occurrences INDEX5 à exclure
    occurrences_exclues = {
        'SYNC_pair_4_TIE',
        'SYNC_pair_6_TIE',
        'SYNC_impair_5_TIE',
        'DESYNC_pair_4_TIE',
        'DESYNC_pair_6_TIE',
        'DESYNC_impair_5_TIE'
    }

    print(f"🚫 Exclusion de {len(occurrences_exclues)} occurrences TIE :")
    for occurrence in sorted(occurrences_exclues):
        print(f"   ❌ {occurrence}")

    try:
        # Essayer fichiers disponibles
        fichiers_test = [
            "dataset_baccarat_lupasco_20250617_232800.json",
            "dataset_test_3_parties_complet.json"
        ]
        
        for fichier in fichiers_test:
            try:
                print(f"🔍 Tentative : {fichier}")
                
                if "test_3_parties" in fichier:
                    # Chargement direct petit fichier
                    import json
                    with open(fichier, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    sequence_index5_brute = []
                    for partie in data['parties']:
                        for main in partie['mains']:
                            if 'index5_combined' in main and main['index5_combined']:
                                sequence_index5_brute.append(main['index5_combined'])

                    # Filtrer les occurrences exclues
                    sequence_index5_filtree = [x for x in sequence_index5_brute
                                             if x not in occurrences_exclues]

                    # Analyser les exclusions en détail
                    analyser_exclusions_tie(sequence_index5_brute, sequence_index5_filtree)

                    return sequence_index5_filtree, None  # Pas d'analyse préalable
                else:
                    # Chargement streaming gros fichier
                    from lupasco_refactored.core.data_loader import DataLoader
                    loader = DataLoader(fichier)
                    resultats = loader.charger_donnees(force_streaming=True)

                    sequence_index5_brute = resultats['sequences']['INDEX5']
                    sequence_clean = [x for x in sequence_index5_brute if x != "__FIN_PARTIE__"]

                    # Filtrer les occurrences exclues
                    sequence_index5_filtree = [x for x in sequence_clean
                                             if x not in occurrences_exclues]

                    # Analyser les exclusions en détail
                    analyser_exclusions_tie(sequence_clean, sequence_index5_filtree)

                    return sequence_index5_filtree, resultats
                    
            except Exception as e:
                print(f"   ❌ Échec : {e}")
                continue
        
        print("❌ Aucun fichier trouvé")
        return None, None
        
    except Exception as e:
        print(f"❌ Erreur générale : {e}")
        return None, None

def analyser_exclusions_tie(sequence_brute, sequence_filtree):
    """Analyse les exclusions TIE pour statistiques"""
    occurrences_exclues = {
        'SYNC_pair_4_TIE',
        'SYNC_pair_6_TIE',
        'SYNC_impair_5_TIE',
        'DESYNC_pair_4_TIE',
        'DESYNC_pair_6_TIE',
        'DESYNC_impair_5_TIE'
    }

    # Compter chaque type d'exclusion
    compteur_exclusions = {}
    for occurrence in occurrences_exclues:
        count = sequence_brute.count(occurrence)
        if count > 0:
            compteur_exclusions[occurrence] = count

    total_exclus = sum(compteur_exclusions.values())
    pourcentage_exclus = (total_exclus / len(sequence_brute)) * 100 if sequence_brute else 0

    print(f"\n📊 DÉTAIL DES EXCLUSIONS TIE :")
    print("-" * 40)
    for occurrence, count in sorted(compteur_exclusions.items()):
        pct = (count / len(sequence_brute)) * 100 if sequence_brute else 0
        print(f"   ❌ {occurrence:<20} : {count:4,} ({pct:.1f}%)")

    print(f"\n📈 RÉSUMÉ EXCLUSIONS :")
    print(f"   🔢 Total exclus : {total_exclus:,} ({pourcentage_exclus:.1f}%)")
    print(f"   ✅ Éléments analysables : {len(sequence_filtree):,}")
    print(f"   📊 Taux de rétention : {(len(sequence_filtree)/len(sequence_brute))*100:.1f}%")

    return compteur_exclusions

def configurer_exploiteur_adaptatif(taille_echantillon):
    """Configure l'exploiteur selon la taille de l'échantillon"""
    from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
    
    exploiteur = PatternExploiter()
    
    # Ajuster seuils selon taille échantillon
    if taille_echantillon < 1000:
        # Petit échantillon : seuils très bas
        exploiteur.seuil_confiance = 0.30
        exploiteur.seuil_cycles = 0.20
        exploiteur.seuil_dependances = 0.25
        exploiteur.min_occurrences_markov = 3
        exploiteur.min_occurrences_deps = 5
        niveau = "TRÈS_BAS"
    elif taille_echantillon < 10000:
        # Échantillon moyen : seuils bas
        exploiteur.seuil_confiance = 0.35
        exploiteur.seuil_cycles = 0.25
        exploiteur.seuil_dependances = 0.30
        exploiteur.min_occurrences_markov = 5
        exploiteur.min_occurrences_deps = 8
        niveau = "BAS"
    elif taille_echantillon < 100000:
        # Grand échantillon : seuils modérés
        exploiteur.seuil_confiance = 0.40
        exploiteur.seuil_cycles = 0.30
        exploiteur.seuil_dependances = 0.35
        exploiteur.min_occurrences_markov = 10
        exploiteur.min_occurrences_deps = 15
        niveau = "MODÉRÉ"
    else:
        # Très grand échantillon : seuils standards
        exploiteur.seuil_confiance = 0.45
        exploiteur.seuil_cycles = 0.35
        exploiteur.seuil_dependances = 0.40
        exploiteur.min_occurrences_markov = 20
        exploiteur.min_occurrences_deps = 30
        niveau = "STANDARD"
    
    print(f"⚙️  CONFIGURATION ADAPTATIVE ({niveau}) :")
    print(f"   🎯 Confiance Markov : {exploiteur.seuil_confiance:.0%}")
    print(f"   📈 Cycles : {exploiteur.seuil_cycles:.0%}")
    print(f"   🔍 Dépendances : {exploiteur.seuil_dependances:.0%}")
    print(f"   📊 Min. occ. Markov : {exploiteur.min_occurrences_markov}")
    print(f"   📋 Min. occ. dépendances : {exploiteur.min_occurrences_deps}")
    
    return exploiteur

def exploiter_patterns_optimise(sequence_index5):
    """Exploitation optimisée des patterns"""
    print("\n🎯 EXPLOITATION PATTERNS OPTIMISÉE")
    print("=" * 50)
    
    try:
        # Configuration adaptative
        exploiteur = configurer_exploiteur_adaptatif(len(sequence_index5))
        
        print(f"\n🔍 Analyse sur {len(sequence_index5):,} éléments...")
        
        # Analyse patterns (sans résultats d'analyse préalable)
        patterns_exploitables = exploiteur.analyser_patterns_exploitables(
            sequence_index5, {}  # Dictionnaire vide car pas d'analyse préalable
        )
        
        # Affichage résultats
        print(f"\n📊 RÉSULTATS :")
        nb_markov = len(patterns_exploitables['patterns_markov_haute_confiance'])
        nb_cycles = len(patterns_exploitables['patterns_cycles_predictibles'])
        nb_deps = len(patterns_exploitables['patterns_dependances_cachees'])
        nb_strategies = len(patterns_exploitables['strategies_recommandees'])
        
        print(f"   🔗 Patterns Markov : {nb_markov}")
        print(f"   📈 Cycles : {nb_cycles}")
        print(f"   🔍 Dépendances : {nb_deps}")
        print(f"   🚀 Stratégies : {nb_strategies}")
        
        total_patterns = nb_markov + nb_cycles + nb_deps
        
        if total_patterns == 0:
            print(f"\n🔧 AJUSTEMENT AUTOMATIQUE...")
            patterns_exploitables = ajuster_et_reessayer(sequence_index5, exploiteur)
            
            # Recompter après ajustement
            nb_markov = len(patterns_exploitables['patterns_markov_haute_confiance'])
            nb_cycles = len(patterns_exploitables['patterns_cycles_predictibles'])
            nb_deps = len(patterns_exploitables['patterns_dependances_cachees'])
            total_patterns = nb_markov + nb_cycles + nb_deps
            
            print(f"   ✅ Après ajustement : {total_patterns} patterns")
        
        return patterns_exploitables, exploiteur
        
    except Exception as e:
        print(f"❌ Erreur exploitation : {e}")
        traceback.print_exc()
        return None, None

def ajuster_et_reessayer(sequence_index5, exploiteur):
    """Ajustement automatique des seuils"""
    
    # Seuils progressivement plus permissifs
    ajustements = [
        (0.25, 0.15, 0.20, 3, 5),   # Très permissif
        (0.20, 0.10, 0.15, 2, 3),   # Extrêmement permissif
        (0.15, 0.05, 0.10, 1, 2)    # Minimal
    ]
    
    for i, (conf, cycles, deps, min_markov, min_deps) in enumerate(ajustements, 1):
        print(f"   🔄 Tentative {i} : Confiance {conf:.0%}")
        
        exploiteur.seuil_confiance = conf
        exploiteur.seuil_cycles = cycles
        exploiteur.seuil_dependances = deps
        exploiteur.min_occurrences_markov = min_markov
        exploiteur.min_occurrences_deps = min_deps
        
        patterns = exploiteur.analyser_patterns_exploitables(sequence_index5, {})
        
        total = (len(patterns['patterns_markov_haute_confiance']) + 
                len(patterns['patterns_cycles_predictibles']) + 
                len(patterns['patterns_dependances_cachees']))
        
        if total > 0:
            print(f"   ✅ Succès ! {total} patterns trouvés")
            return patterns
    
    # Si toujours rien, retourner structure vide
    return {
        'patterns_markov_haute_confiance': [],
        'patterns_cycles_predictibles': [],
        'patterns_dependances_cachees': [],
        'strategies_recommandees': [],
        'zones_opportunite': []
    }

def generer_rapports_complets(patterns_exploitables, exploiteur, sequence_index5):
    """Génère tous les rapports d'exploitation"""
    print("\n📋 GÉNÉRATION RAPPORTS COMPLETS")
    print("=" * 50)
    
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. Sauvegarder patterns JSON
        patterns_avec_config = patterns_exploitables.copy()
        patterns_avec_config['configuration_finale'] = {
            'seuil_confiance': exploiteur.seuil_confiance,
            'seuil_cycles': exploiteur.seuil_cycles,
            'seuil_dependances': exploiteur.seuil_dependances,
            'min_occurrences_markov': exploiteur.min_occurrences_markov,
            'min_occurrences_deps': exploiteur.min_occurrences_deps,
            'taille_echantillon': len(sequence_index5)
        }
        
        nom_json = exploiteur.sauvegarder_patterns_exploitables(
            patterns_avec_config, f"optimise_{timestamp}"
        )
        
        # 2. Rapport d'exploitation principal
        nom_rapport = exploiteur.generer_rapport_exploitation(
            patterns_exploitables, f"optimise_{timestamp}"
        )
        
        # 3. Guide pratique détaillé
        nom_guide = f"guide_exploitation_optimise_{timestamp}.txt"
        
        with open(nom_guide, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("GUIDE PRATIQUE D'EXPLOITATION OPTIMISÉ - INDEX5 (SANS TIE)\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Échantillon analysé : {len(sequence_index5):,} éléments INDEX5\n\n")

            f.write("🚫 EXCLUSIONS APPLIQUÉES :\n")
            f.write("-" * 30 + "\n")
            f.write("Les 6 occurrences TIE ont été automatiquement exclues :\n")
            f.write("- SYNC_pair_4_TIE, SYNC_pair_6_TIE, SYNC_impair_5_TIE\n")
            f.write("- DESYNC_pair_4_TIE, DESYNC_pair_6_TIE, DESYNC_impair_5_TIE\n")
            f.write("Raison : Focus sur les patterns PLAYER/BANKER plus prédictibles\n\n")
            
            # Configuration utilisée
            f.write("⚙️  CONFIGURATION UTILISÉE :\n")
            f.write("-" * 30 + "\n")
            f.write(f"Seuil confiance Markov : {exploiteur.seuil_confiance:.0%}\n")
            f.write(f"Seuil cycles : {exploiteur.seuil_cycles:.0%}\n")
            f.write(f"Seuil dépendances : {exploiteur.seuil_dependances:.0%}\n")
            f.write(f"Min. occurrences Markov : {exploiteur.min_occurrences_markov}\n")
            f.write(f"Min. occurrences dépendances : {exploiteur.min_occurrences_deps}\n\n")
            
            # Résumé patterns
            nb_markov = len(patterns_exploitables['patterns_markov_haute_confiance'])
            nb_cycles = len(patterns_exploitables['patterns_cycles_predictibles'])
            nb_deps = len(patterns_exploitables['patterns_dependances_cachees'])
            
            f.write("📊 RÉSUMÉ PATTERNS DÉTECTÉS :\n")
            f.write("-" * 30 + "\n")
            f.write(f"Patterns Markov : {nb_markov}\n")
            f.write(f"Cycles prédictibles : {nb_cycles}\n")
            f.write(f"Dépendances cachées : {nb_deps}\n")
            f.write(f"Total exploitable : {nb_markov + nb_cycles + nb_deps}\n\n")
            
            # Instructions d'utilisation
            f.write("🎯 INSTRUCTIONS D'UTILISATION :\n")
            f.write("-" * 30 + "\n")
            f.write("1. Consultez le rapport détaillé pour les patterns spécifiques\n")
            f.write("2. Utilisez les stratégies recommandées selon votre profil de risque\n")
            f.write("3. Surveillez les zones d'opportunité maximale\n")
            f.write("4. Adaptez les seuils selon vos résultats\n\n")
            
            if nb_markov + nb_cycles + nb_deps == 0:
                f.write("⚠️  AUCUN PATTERN EXPLOITABLE DÉTECTÉ\n")
                f.write("-" * 40 + "\n")
                f.write("Causes possibles :\n")
                f.write("- Échantillon trop petit\n")
                f.write("- Données trop aléatoires\n")
                f.write("- Seuils encore trop élevés\n\n")
                f.write("Recommandations :\n")
                f.write("- Augmenter la taille de l'échantillon\n")
                f.write("- Réduire encore les seuils de confiance\n")
                f.write("- Analyser des sous-ensembles spécifiques\n")
        
        print(f"📄 JSON : {nom_json}")
        print(f"📋 Rapport : {nom_rapport}")
        print(f"📖 Guide : {nom_guide}")
        
        return {
            'json': nom_json,
            'rapport': nom_rapport,
            'guide': nom_guide
        }
        
    except Exception as e:
        print(f"❌ Erreur génération rapports : {e}")
        return {}

def main():
    """Fonction principale optimisée"""
    print("🎯 EXPLOITATION PATTERNS INDEX5 - VERSION OPTIMISÉE (SANS TIE)")
    print("=" * 80)
    print("Utilisation optimale du moteur pattern_exploitation.py")
    print("🚫 Exclusion automatique des 6 occurrences TIE")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Phase 1: Chargement
    sequence_index5, _ = charger_donnees_optimise()
    if not sequence_index5:
        print("❌ Échec chargement")
        return False
    
    # Phase 2: Exploitation optimisée
    patterns_exploitables, exploiteur = exploiter_patterns_optimise(sequence_index5)
    if not patterns_exploitables:
        print("❌ Échec exploitation")
        return False
    
    # Phase 3: Rapports complets
    fichiers = generer_rapports_complets(patterns_exploitables, exploiteur, sequence_index5)
    
    # Résumé final
    print("\n" + "=" * 80)
    print("🎉 EXPLOITATION OPTIMISÉE TERMINÉE")
    print("=" * 80)
    
    total_patterns = (len(patterns_exploitables['patterns_markov_haute_confiance']) + 
                     len(patterns_exploitables['patterns_cycles_predictibles']) + 
                     len(patterns_exploitables['patterns_dependances_cachees']))
    
    print(f"📊 Échantillon analysé : {len(sequence_index5):,} éléments")
    print(f"🎯 Patterns exploitables : {total_patterns}")
    print(f"🚀 Stratégies générées : {len(patterns_exploitables['strategies_recommandees'])}")
    
    if fichiers:
        print(f"\n📁 FICHIERS GÉNÉRÉS :")
        for type_fichier, nom in fichiers.items():
            print(f"   📄 {type_fichier.upper()} : {nom}")
    
    if total_patterns > 0:
        print(f"\n✅ SUCCÈS : Patterns exploitables détectés !")
        print(f"📋 Consultez le rapport d'exploitation pour les détails")
    else:
        print(f"\n⚠️  AUCUN PATTERN : Échantillon trop aléatoire ou petit")
        print(f"💡 Essayez avec un échantillon plus grand")
    
    print("=" * 80)
    
    return total_patterns > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
