"""
INDEX5 ANOMALY DETECTION MODULE
===============================

Module spécialisé pour la détection d'anomalies dans INDEX5
Implémente les recommandations de base.txt :
- Détection d'anomalies : Exploiter les écarts de fragmentation
- Optimisation entropique : Maximiser l'information tout en préservant la structure

Techniques adaptées pour les 18 combinaisons INDEX5 :
- Détection d'écarts de fragmentation séquentielle
- Isolation Forest adapté aux patterns INDEX5
- Détection de changements de régime entropique
- Analyse des singularités statistiques

Auteur : Expert Statisticien IA
Date : 2025-06-20
Version : 1.0
"""

import numpy as np
import pandas as pd
from scipy import stats
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Any
import math
import warnings

try:
    from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, diviser_en_parties
except ImportError:
    # Fallback pour compatibilité
    def nettoyer_marqueurs(sequence):
        return [x for x in sequence if x != "__FIN_PARTIE__"]

    def diviser_en_parties(sequence):
        parties = []
        partie_actuelle = []
        for element in sequence:
            if element == "__FIN_PARTIE__":
                if partie_actuelle:
                    parties.append(partie_actuelle)
                    partie_actuelle = []
            else:
                partie_actuelle.append(element)
        if partie_actuelle:
            parties.append(partie_actuelle)
        return parties


class Index5AnomalyDetector:
    """
    Détecteur d'anomalies spécialisé pour INDEX5
    
    Implémente les recommandations base.txt :
    1. Détection d'écarts de fragmentation
    2. Optimisation entropique
    3. Surveillance des patterns INDEX5
    """
    
    def __init__(self):
        """Initialise le détecteur d'anomalies INDEX5"""
        self.combinaisons_index5 = [
            'SYNC_impair_5_BANKER', 'SYNC_impair_5_PLAYER', 'SYNC_impair_5_TIE',
            'SYNC_pair_4_BANKER', 'SYNC_pair_4_PLAYER', 'SYNC_pair_4_TIE',
            'SYNC_pair_6_BANKER', 'SYNC_pair_6_PLAYER', 'SYNC_pair_6_TIE',
            'DESYNC_impair_5_BANKER', 'DESYNC_impair_5_PLAYER', 'DESYNC_impair_5_TIE',
            'DESYNC_pair_4_BANKER', 'DESYNC_pair_4_PLAYER', 'DESYNC_pair_4_TIE',
            'DESYNC_pair_6_BANKER', 'DESYNC_pair_6_PLAYER', 'DESYNC_pair_6_TIE'
        ]
        
        # Profils de référence INDEX5 (basés sur analyse précédente)
        self.profils_reference = {
            'frequences_normales': {combo: 1/18 for combo in self.combinaisons_index5},
            'longueurs_runs_normales': {combo: 1.15 for combo in self.combinaisons_index5},
            'entropie_normale': 3.98,  # Entropie INDEX5 normale
            'fragmentation_normale': 11.5  # Écart fragmentation normal
        }
        
        # Seuils d'anomalie calibrés pour INDEX5
        self.seuils_anomalie = {
            'frequence_z_score': 3.0,
            'runs_deviation': 0.5,
            'entropie_deviation': 0.2,
            'fragmentation_extreme': 20.0
        }
    
    def detecter_anomalies_index5(self, sequence_index5: List[str]) -> Dict:
        """
        Détection complète d'anomalies INDEX5
        
        Args:
            sequence_index5: Séquence INDEX5 brute avec marqueurs
            
        Returns:
            dict: Résultats complets de détection d'anomalies
        """
        print("\n🚨 DÉTECTION ANOMALIES INDEX5 - RECOMMANDATIONS BASE.TXT")
        print("=" * 60)
        
        # Nettoyer la séquence
        sequence_propre = nettoyer_marqueurs(sequence_index5)
        
        if len(sequence_propre) < 50:
            return {'erreur': 'Séquence INDEX5 trop courte pour détection anomalies'}
        
        resultats = {
            'anomalies_detectees': [],
            'score_anomalie_global': 0.0,
            'classification_risque': 'NORMAL',
            'recommandations_action': []
        }
        
        # 1. DÉTECTION ÉCARTS DE FRAGMENTATION
        print("   🔍 1. Détection écarts fragmentation INDEX5...")
        anomalies_fragmentation = self._detecter_anomalies_fragmentation(sequence_propre)
        resultats['anomalies_fragmentation'] = anomalies_fragmentation
        
        # 2. DÉTECTION ANOMALIES FRÉQUENTIELLES
        print("   📊 2. Détection anomalies fréquentielles INDEX5...")
        anomalies_frequences = self._detecter_anomalies_frequences(sequence_propre)
        resultats['anomalies_frequences'] = anomalies_frequences
        
        # 3. DÉTECTION CHANGEMENTS RÉGIME ENTROPIQUE
        print("   📈 3. Détection changements régime entropique INDEX5...")
        anomalies_entropie = self._detecter_anomalies_entropie(sequence_propre)
        resultats['anomalies_entropie'] = anomalies_entropie
        
        # 4. ISOLATION FOREST ADAPTÉ INDEX5
        print("   🌲 4. Isolation Forest adapté INDEX5...")
        anomalies_isolation = self._detecter_anomalies_isolation_index5(sequence_propre)
        resultats['anomalies_isolation'] = anomalies_isolation
        
        # 5. SYNTHÈSE ET CLASSIFICATION
        resultats = self._synthetiser_anomalies_index5(resultats)
        
        print(f"   ✅ Détection anomalies INDEX5 terminée - Score: {resultats['score_anomalie_global']:.2f}")
        return resultats
    
    def _detecter_anomalies_fragmentation(self, sequence: List[str]) -> Dict:
        """
        Détecte les anomalies de fragmentation INDEX5
        Implémente : "Exploiter les écarts de fragmentation"
        """
        resultats = {
            'ecarts_fragmentation': {},
            'anomalies_detectees': [],
            'score_fragmentation': 0.0
        }
        
        # Analyser les runs par combinaison
        runs_par_combinaison = self._analyser_runs_par_combinaison(sequence)
        
        anomalies_count = 0
        ecarts_totaux = []
        
        for combinaison in self.combinaisons_index5:
            if combinaison in runs_par_combinaison:
                runs_data = runs_par_combinaison[combinaison]
                longueur_moyenne = runs_data.get('longueur_moyenne', 0)
                longueur_theorique = runs_data.get('longueur_theorique', 1)
                
                # Calculer écart de fragmentation
                ecart_fragmentation = abs(longueur_theorique - longueur_moyenne)
                resultats['ecarts_fragmentation'][combinaison] = ecart_fragmentation
                ecarts_totaux.append(ecart_fragmentation)
                
                # Détecter anomalie si écart extrême
                if ecart_fragmentation > self.seuils_anomalie['fragmentation_extreme']:
                    anomalies_count += 1
                    resultats['anomalies_detectees'].append({
                        'type': 'FRAGMENTATION_EXTREME',
                        'combinaison': combinaison,
                        'ecart': ecart_fragmentation,
                        'severite': 'ÉLEVÉE' if ecart_fragmentation > 50 else 'MODÉRÉE'
                    })
        
        # Score global de fragmentation
        if ecarts_totaux:
            resultats['score_fragmentation'] = np.mean(ecarts_totaux)
        
        return resultats
    
    def _detecter_anomalies_frequences(self, sequence: List[str]) -> Dict:
        """
        Détecte les anomalies dans les fréquences des combinaisons INDEX5
        """
        resultats = {
            'frequences_observees': {},
            'z_scores': {},
            'anomalies_detectees': [],
            'score_frequentiel': 0.0
        }
        
        # Calculer fréquences observées
        compteur = Counter(sequence)
        total = len(sequence)
        
        z_scores_absolus = []
        anomalies_count = 0
        
        for combinaison in self.combinaisons_index5:
            freq_observee = compteur.get(combinaison, 0) / total
            freq_attendue = self.profils_reference['frequences_normales'][combinaison]
            
            resultats['frequences_observees'][combinaison] = freq_observee
            
            # Calculer Z-score (test binomial)
            n = total
            p = freq_attendue
            variance = n * p * (1 - p)
            
            if variance > 0:
                z_score = (compteur.get(combinaison, 0) - n * p) / math.sqrt(variance)
                resultats['z_scores'][combinaison] = z_score
                z_scores_absolus.append(abs(z_score))
                
                # Détecter anomalie
                if abs(z_score) > self.seuils_anomalie['frequence_z_score']:
                    anomalies_count += 1
                    resultats['anomalies_detectees'].append({
                        'type': 'FREQUENCE_ANORMALE',
                        'combinaison': combinaison,
                        'z_score': z_score,
                        'freq_observee': freq_observee,
                        'freq_attendue': freq_attendue,
                        'severite': 'CRITIQUE' if abs(z_score) > 5 else 'ÉLEVÉE'
                    })
        
        # Score fréquentiel global
        if z_scores_absolus:
            resultats['score_frequentiel'] = np.mean(z_scores_absolus)
        
        return resultats
    
    def _detecter_anomalies_entropie(self, sequence: List[str]) -> Dict:
        """
        Détecte les changements de régime entropique INDEX5
        """
        resultats = {
            'entropies_temporelles': [],
            'changements_regime': [],
            'anomalies_detectees': [],
            'score_entropique': 0.0
        }
        
        # Analyse entropique par fenêtres glissantes
        taille_fenetre = min(200, len(sequence) // 10)
        if taille_fenetre < 50:
            return resultats
        
        entropies = []
        positions = []
        
        for i in range(0, len(sequence) - taille_fenetre + 1, taille_fenetre // 2):
            fenetre = sequence[i:i + taille_fenetre]
            entropie = self._calculer_entropie_shannon(fenetre)
            entropies.append(entropie)
            positions.append(i + taille_fenetre // 2)
        
        resultats['entropies_temporelles'] = list(zip(positions, entropies))
        
        # Détecter changements de régime
        if len(entropies) >= 3:
            # Détecter sauts d'entropie
            for i in range(1, len(entropies)):
                diff_entropie = abs(entropies[i] - entropies[i-1])
                
                if diff_entropie > self.seuils_anomalie['entropie_deviation']:
                    resultats['changements_regime'].append({
                        'position': positions[i],
                        'entropie_avant': entropies[i-1],
                        'entropie_apres': entropies[i],
                        'amplitude_changement': diff_entropie
                    })
                    
                    resultats['anomalies_detectees'].append({
                        'type': 'CHANGEMENT_REGIME_ENTROPIQUE',
                        'position': positions[i],
                        'amplitude': diff_entropie,
                        'severite': 'CRITIQUE' if diff_entropie > 0.5 else 'MODÉRÉE'
                    })
        
        # Score entropique
        if entropies:
            variance_entropie = np.var(entropies)
            resultats['score_entropique'] = variance_entropie
        
        return resultats
    
    def _detecter_anomalies_isolation_index5(self, sequence: List[str]) -> Dict:
        """
        Isolation Forest adapté pour INDEX5
        Détecte les patterns anormaux dans les séquences
        """
        resultats = {
            'patterns_anormaux': [],
            'scores_isolation': [],
            'anomalies_detectees': [],
            'score_isolation': 0.0
        }
        
        # Créer features pour Isolation Forest
        features = self._extraire_features_index5(sequence)
        
        if len(features) < 10:
            return resultats
        
        # Implémentation simplifiée d'Isolation Forest
        # (Version complète nécessiterait sklearn)
        anomalies_isolation = self._isolation_forest_simple(features)
        
        resultats['scores_isolation'] = anomalies_isolation
        
        # Identifier anomalies (scores < -0.5)
        seuil_anomalie = -0.5
        for i, score in enumerate(anomalies_isolation):
            if score < seuil_anomalie:
                resultats['anomalies_detectees'].append({
                    'type': 'PATTERN_ANORMAL_ISOLATION',
                    'position': i,
                    'score_isolation': score,
                    'severite': 'ÉLEVÉE' if score < -0.7 else 'MODÉRÉE'
                })
        
        # Score global
        if anomalies_isolation:
            resultats['score_isolation'] = np.mean([abs(s) for s in anomalies_isolation if s < 0])
        
        return resultats
    
    def _analyser_runs_par_combinaison(self, sequence: List[str]) -> Dict:
        """Analyse les runs pour chaque combinaison INDEX5"""
        runs_data = {}
        
        for combinaison in self.combinaisons_index5:
            # Créer séquence binaire pour cette combinaison
            sequence_binaire = [1 if x == combinaison else 0 for x in sequence]
            
            # Analyser les runs
            runs = []
            current_run = 0
            
            for val in sequence_binaire:
                if val == 1:
                    current_run += 1
                else:
                    if current_run > 0:
                        runs.append(current_run)
                        current_run = 0
            
            if current_run > 0:
                runs.append(current_run)
            
            if runs:
                # Calculer statistiques
                longueur_moyenne = np.mean(runs)
                prob = sequence.count(combinaison) / len(sequence)
                longueur_theorique = 1 / prob if prob > 0 else 1
                
                runs_data[combinaison] = {
                    'runs': runs,
                    'longueur_moyenne': longueur_moyenne,
                    'longueur_theorique': longueur_theorique,
                    'nb_runs': len(runs),
                    'probabilite': prob
                }
        
        return runs_data
    
    def _calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """Calcule l'entropie de Shannon pour une séquence"""
        if not sequence:
            return 0.0
        
        compteur = Counter(sequence)
        total = len(sequence)
        
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        
        return entropie
    
    def _extraire_features_index5(self, sequence: List[str]) -> List[List[float]]:
        """Extrait des features pour Isolation Forest INDEX5"""
        features = []
        taille_fenetre = 50
        
        for i in range(0, len(sequence) - taille_fenetre + 1, 10):
            fenetre = sequence[i:i + taille_fenetre]
            
            # Features : fréquences des combinaisons
            compteur = Counter(fenetre)
            feature_vector = []
            
            for combinaison in self.combinaisons_index5:
                freq = compteur.get(combinaison, 0) / taille_fenetre
                feature_vector.append(freq)
            
            # Ajouter features statistiques
            entropie = self._calculer_entropie_shannon(fenetre)
            nb_uniques = len(set(fenetre))
            
            feature_vector.extend([entropie, nb_uniques])
            features.append(feature_vector)
        
        return features
    
    def _isolation_forest_simple(self, features: List[List[float]]) -> List[float]:
        """Implémentation simplifiée d'Isolation Forest"""
        if not features:
            return []
        
        # Normaliser les features
        features_array = np.array(features)
        mean_features = np.mean(features_array, axis=0)
        std_features = np.std(features_array, axis=0)
        
        scores = []
        
        for feature_vector in features_array:
            # Score basé sur distance à la moyenne
            distances = []
            for i, (val, mean_val, std_val) in enumerate(zip(feature_vector, mean_features, std_features)):
                if std_val > 0:
                    z_score = abs(val - mean_val) / std_val
                    distances.append(z_score)
            
            # Score d'isolation (négatif = anormal)
            if distances:
                score = -np.mean(distances) / 3.0  # Normaliser
                scores.append(max(score, -1.0))  # Limiter à -1
            else:
                scores.append(0.0)
        
        return scores
    
    def _synthetiser_anomalies_index5(self, resultats: Dict) -> Dict:
        """Synthèse globale des anomalies INDEX5"""
        # Compter toutes les anomalies
        total_anomalies = 0
        anomalies_critiques = 0
        
        for key in ['anomalies_fragmentation', 'anomalies_frequences', 'anomalies_entropie', 'anomalies_isolation']:
            if key in resultats:
                anomalies = resultats[key].get('anomalies_detectees', [])
                total_anomalies += len(anomalies)
                anomalies_critiques += sum(1 for a in anomalies if a.get('severite') == 'CRITIQUE')
        
        # Calculer score global (0-100)
        score_fragmentation = resultats.get('anomalies_fragmentation', {}).get('score_fragmentation', 0)
        score_frequentiel = resultats.get('anomalies_frequences', {}).get('score_frequentiel', 0)
        score_entropique = resultats.get('anomalies_entropie', {}).get('score_entropique', 0) * 100
        score_isolation = resultats.get('anomalies_isolation', {}).get('score_isolation', 0) * 100
        
        score_global = min((score_fragmentation + score_frequentiel + score_entropique + score_isolation) / 4, 100)
        
        # Classification du risque
        if score_global < 20 and anomalies_critiques == 0:
            classification = 'NORMAL'
        elif score_global < 50 and anomalies_critiques <= 1:
            classification = 'ATTENTION'
        elif score_global < 80 and anomalies_critiques <= 3:
            classification = 'RISQUE_ÉLEVÉ'
        else:
            classification = 'RISQUE_CRITIQUE'
        
        # Recommandations
        recommandations = []
        if total_anomalies > 5:
            recommandations.append("Surveillance renforcée INDEX5 recommandée")
        if anomalies_critiques > 0:
            recommandations.append("Intervention immédiate requise - Anomalies critiques détectées")
        if score_fragmentation > 30:
            recommandations.append("Analyser les causes de fragmentation excessive")
        if score_frequentiel > 3:
            recommandations.append("Vérifier l'équilibre des combinaisons INDEX5")
        
        # Consolider toutes les anomalies
        toutes_anomalies = []
        for key in ['anomalies_fragmentation', 'anomalies_frequences', 'anomalies_entropie', 'anomalies_isolation']:
            if key in resultats:
                toutes_anomalies.extend(resultats[key].get('anomalies_detectees', []))
        
        resultats['anomalies_detectees'] = toutes_anomalies
        resultats['score_anomalie_global'] = score_global
        resultats['classification_risque'] = classification
        resultats['recommandations_action'] = recommandations
        resultats['nb_anomalies_total'] = total_anomalies
        resultats['nb_anomalies_critiques'] = anomalies_critiques
        
        return resultats


# Fonctions d'export pour compatibilité
__all__ = [
    'Index5AnomalyDetector'
]
