"""
CORE - SEQUENCE EXTRACTOR
==========================

Module contenant la logique d'extraction des composants de séquences
pour l'analyseur Lupasco refactorisé.

Classe SequenceExtractor pour extraire les composants INDEX depuis les séquences combinées.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

from typing import Dict, List
from lupasco_refactored.utils.data_utils import nettoyer_marqueurs


class SequenceExtractor:
    """
    Classe pour extraire les composants INDEX depuis les séquences combinées
    Supporte l'extraction depuis INDEX5, INDEX2_INDEX3, et INDEX1_INDEX3
    """
    
    def __init__(self, sequences: Dict[str, List[str]]):
        """
        Initialise le SequenceExtractor
        
        Args:
            sequences: Dictionnaire des séquences {nom: liste_valeurs}
        """
        self.sequences = sequences
        
    def extraire_composants_index5(self) -> Dict[str, List[str]]:
        """
        Extrait INDEX1, INDEX2, INDEX3 depuis self.sequences['INDEX5']
        Format: "SYNC_pair_4_BANKER" → ["SYNC", "pair_4", "BANKER"]
        Gestion des formats variables d'INDEX5
        
        Returns:
            dict: {'INDEX1': [...], 'INDEX2': [...], 'INDEX3': [...]}
        """
        if 'INDEX5' not in self.sequences:
            raise ValueError("Séquence INDEX5 non trouvée")
            
        index5_data = nettoyer_marqueurs(self.sequences['INDEX5'])
        
        index1_extracted = []
        index2_extracted = []
        index3_extracted = []
        
        # Debug : examiner le format réel
        if index5_data:
            print(f"🔍 DEBUG: Format INDEX5 détecté - Premier élément: '{index5_data[0]}'")
            sample_parts = index5_data[0].split('_')
            print(f"🔍 DEBUG: Nombre de parties: {len(sample_parts)}, Parties: {sample_parts}")
        
        for combinaison in index5_data:
            parts = combinaison.split('_')
            
            # Gérer les différents formats possibles
            if len(parts) == 3:
                # Format: "SYNC_pair_4_BANKER"
                index1_extracted.append(parts[0])  # SYNC/DESYNC
                index2_extracted.append(parts[1])  # pair_4/pair_6/impair_5
                index3_extracted.append(parts[2])  # BANKER/PLAYER/TIE
            elif len(parts) == 4:
                # Format: "SYNC_pair_4_BANKER" avec underscore dans INDEX2
                index1_extracted.append(parts[0])  # SYNC/DESYNC
                index2_extracted.append(f"{parts[1]}_{parts[2]}")  # pair_4/impair_5/pair_6
                index3_extracted.append(parts[3])  # BANKER/PLAYER/TIE
            else:
                print(f"⚠️ Format INDEX5 inattendu: '{combinaison}' ({len(parts)} parties)")
        
        print(f"🔍 DEBUG: Extraction terminée - {len(index1_extracted)} éléments extraits")
        if index1_extracted:
            print(f"🔍 DEBUG: INDEX1 échantillon: {index1_extracted[:5]}")
            print(f"🔍 DEBUG: INDEX2 échantillon: {index2_extracted[:5]}")
            print(f"🔍 DEBUG: INDEX3 échantillon: {index3_extracted[:5]}")
        
        return {
            'INDEX1': index1_extracted,
            'INDEX2': index2_extracted,
            'INDEX3': index3_extracted
        }
    
    def extraire_composants_index2_index3(self) -> Dict[str, List[str]]:
        """
        Extrait INDEX2, INDEX3 depuis self.sequences['INDEX2_INDEX3']
        Format: "pair_4_BANKER" → ["pair_4", "BANKER"]
        Gestion du format 3 parties détecté
        
        Returns:
            dict: {'INDEX2': [...], 'INDEX3': [...]}
        """
        if 'INDEX2_INDEX3' not in self.sequences:
            raise ValueError("Séquence INDEX2_INDEX3 non trouvée")
            
        index2_index3_data = nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        
        index2_extracted = []
        index3_extracted = []
        
        # Debug : examiner le format réel
        if index2_index3_data:
            print(f"🔍 DEBUG: Format INDEX2_INDEX3 détecté - Premier élément: '{index2_index3_data[0]}'")
            sample_parts = index2_index3_data[0].split('_')
            print(f"🔍 DEBUG: Nombre de parties: {len(sample_parts)}, Parties: {sample_parts}")
        
        for combinaison in index2_index3_data:
            parts = combinaison.split('_')
            
            # Gérer le format 3 parties détecté
            if len(parts) == 2:
                # Format attendu: "pair_4_BANKER"
                index2_extracted.append(parts[0])  # pair_4/pair_6/impair_5
                index3_extracted.append(parts[1])  # BANKER/PLAYER/TIE
            elif len(parts) == 3:
                # Format réel détecté: "pair_4_BANKER"
                index2_extracted.append(f"{parts[0]}_{parts[1]}")  # pair_4/pair_6/impair_5
                index3_extracted.append(parts[2])  # BANKER/PLAYER/TIE
            else:
                print(f"⚠️ Format INDEX2_INDEX3 inattendu: '{combinaison}' ({len(parts)} parties)")
        
        print(f"🔍 DEBUG: Extraction INDEX2_INDEX3 terminée - {len(index2_extracted)} éléments extraits")
        if index2_extracted:
            print(f"🔍 DEBUG: INDEX2 échantillon: {index2_extracted[:5]}")
            print(f"🔍 DEBUG: INDEX3 échantillon: {index3_extracted[:5]}")
        
        return {
            'INDEX2': index2_extracted,
            'INDEX3': index3_extracted
        }
    
    def extraire_composants_index1_index3(self) -> Dict[str, List[str]]:
        """
        Extrait INDEX1, INDEX3 depuis self.sequences['INDEX1_INDEX3']
        Format: "SYNC_BANKER" → ["SYNC", "BANKER"]
        
        Returns:
            dict: {'INDEX1': [...], 'INDEX3': [...]}
        """
        if 'INDEX1_INDEX3' not in self.sequences:
            raise ValueError("Séquence INDEX1_INDEX3 non trouvée")
            
        index1_index3_data = nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        
        index1_extracted = []
        index3_extracted = []
        
        for combinaison in index1_index3_data:
            parts = combinaison.split('_')
            if len(parts) == 2:
                index1_extracted.append(parts[0])  # SYNC/DESYNC
                index3_extracted.append(parts[1])  # BANKER/PLAYER/TIE
            else:
                print(f"⚠️ Format INDEX1_INDEX3 inattendu: '{combinaison}' ({len(parts)} parties)")
        
        return {
            'INDEX1': index1_extracted,
            'INDEX3': index3_extracted
        }
    
    def extraire_tous_composants(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Extrait tous les composants disponibles depuis toutes les séquences combinées
        
        Returns:
            dict: {
                'INDEX5': {'INDEX1': [...], 'INDEX2': [...], 'INDEX3': [...]},
                'INDEX2_INDEX3': {'INDEX2': [...], 'INDEX3': [...]},
                'INDEX1_INDEX3': {'INDEX1': [...], 'INDEX3': [...]}
            }
        """
        resultats = {}
        
        # Extraction depuis INDEX5
        if 'INDEX5' in self.sequences:
            try:
                resultats['INDEX5'] = self.extraire_composants_index5()
            except Exception as e:
                print(f"⚠️ Erreur extraction INDEX5: {e}")
                resultats['INDEX5'] = None
        
        # Extraction depuis INDEX2_INDEX3
        if 'INDEX2_INDEX3' in self.sequences:
            try:
                resultats['INDEX2_INDEX3'] = self.extraire_composants_index2_index3()
            except Exception as e:
                print(f"⚠️ Erreur extraction INDEX2_INDEX3: {e}")
                resultats['INDEX2_INDEX3'] = None
        
        # Extraction depuis INDEX1_INDEX3
        if 'INDEX1_INDEX3' in self.sequences:
            try:
                resultats['INDEX1_INDEX3'] = self.extraire_composants_index1_index3()
            except Exception as e:
                print(f"⚠️ Erreur extraction INDEX1_INDEX3: {e}")
                resultats['INDEX1_INDEX3'] = None
        
        return resultats
    
    def valider_extractions(self, extractions: Dict[str, Dict[str, List[str]]]) -> Dict[str, bool]:
        """
        Valide la cohérence des extractions
        
        Args:
            extractions: Résultats de extraire_tous_composants()
            
        Returns:
            dict: Résultats de validation par type d'extraction
        """
        validations = {}
        
        # Validation INDEX5
        if 'INDEX5' in extractions and extractions['INDEX5']:
            index5_data = extractions['INDEX5']
            longueurs = [len(index5_data[key]) for key in ['INDEX1', 'INDEX2', 'INDEX3']]
            validations['INDEX5'] = len(set(longueurs)) == 1  # Toutes les longueurs identiques
        
        # Validation INDEX2_INDEX3
        if 'INDEX2_INDEX3' in extractions and extractions['INDEX2_INDEX3']:
            index2_index3_data = extractions['INDEX2_INDEX3']
            longueurs = [len(index2_index3_data[key]) for key in ['INDEX2', 'INDEX3']]
            validations['INDEX2_INDEX3'] = len(set(longueurs)) == 1
        
        # Validation INDEX1_INDEX3
        if 'INDEX1_INDEX3' in extractions and extractions['INDEX1_INDEX3']:
            index1_index3_data = extractions['INDEX1_INDEX3']
            longueurs = [len(index1_index3_data[key]) for key in ['INDEX1', 'INDEX3']]
            validations['INDEX1_INDEX3'] = len(set(longueurs)) == 1
        
        return validations
    
    def comparer_avec_sequences_originales(self, extractions: Dict[str, Dict[str, List[str]]]) -> Dict[str, Dict[str, bool]]:
        """
        Compare les extractions avec les séquences originales si disponibles
        
        Args:
            extractions: Résultats de extraire_tous_composants()
            
        Returns:
            dict: Résultats de comparaison
        """
        comparaisons = {}
        
        # Comparer INDEX5 avec INDEX1, INDEX2, INDEX3 originaux
        if 'INDEX5' in extractions and extractions['INDEX5']:
            index5_data = extractions['INDEX5']
            comparaisons['INDEX5'] = {}
            
            for index_name in ['INDEX1', 'INDEX2', 'INDEX3']:
                if index_name in self.sequences:
                    original_clean = nettoyer_marqueurs(self.sequences[index_name])
                    extracted = index5_data[index_name]
                    comparaisons['INDEX5'][index_name] = original_clean == extracted
                else:
                    comparaisons['INDEX5'][index_name] = None
        
        return comparaisons


# Fonctions d'export pour compatibilité
__all__ = [
    'SequenceExtractor'
]
