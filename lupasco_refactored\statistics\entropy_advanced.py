"""
STATISTICS - ADVANCED ENTROPY ANALYZER
=======================================

Module contenant les 10 équations d'entropie avancées
pour l'analyseur Lupasco refactorisé.

Classe AdvancedEntropy pour analyser les entropies de Rényi,
conditionnelles, information mutuelle, entropie croisée,
divergence KL et autres métriques avancées.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

import numpy as np
import math
from typing import List, Dict, Tuple, Any
from collections import Counter
from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, diviser_en_parties


class AdvancedEntropy:
    """
    Classe pour analyser les entropies avancées selon les 10 équations Lupasco
    Supporte l'entropie de Rényi, conditionnelle, information mutuelle, etc.
    """
    
    def __init__(self):
        """
        Initialise AdvancedEntropy
        """
        pass
    
    def analyser_entropie_avancee_complete(self, sequences: Dict[str, List[str]], 
                                         composants_index5: Dict[str, List[str]] = None) -> Dict:
        """
        MÉTHODE PRINCIPALE - Analyse entropique avancée complète pour tous les indices
        Intègre toutes les 10 équations d'entropie avec les adaptations nécessaires
        
        Args:
            sequences: Dictionnaire des séquences (INDEX5, INDEX2_INDEX3, INDEX1_INDEX3)
            composants_index5: Composants extraits d'INDEX5 (INDEX1, INDEX2, INDEX3)
            
        Returns:
            dict: Résultats complets de l'analyse entropique avancée
        """
        print("\n🔬 === ANALYSE ENTROPIQUE AVANCÉE LUPASCO ===")
        
        resultats = {
            'INDEX5': {},
            'INDEX2_INDEX3': {},
            'INDEX1_INDEX3': {},
            'score_exploitabilite_composite': 0.0,
            'recommandations_strategiques': [],
            'classification_globale': 'NORMAL',
            'parties_exploitables': [],
            'patterns_detectes': []
        }
        
        try:
            # 1. ÉQUATION 1 : ENTROPIE DE RÉNYI
            print("   📊 Équation 1 : Entropie de Rényi...")
            resultats_renyi = self.analyser_entropie_renyi_tous_indices(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_renyi:
                    resultats[indice]['renyi'] = resultats_renyi[indice]
            
            # 2. ÉQUATION 2 : ENTROPIE CONDITIONNELLE
            print("   📊 Équation 2 : Entropie conditionnelle...")
            resultats_conditionnelle = self.analyser_entropie_conditionnelle_prediction(composants_index5)
            resultats['conditionnelle'] = resultats_conditionnelle
            
            # 3. ÉQUATION 3 : INFORMATION MUTUELLE
            print("   📊 Équation 3 : Information mutuelle...")
            resultats_mutuelle = self.analyser_information_mutuelle_complete(sequences, composants_index5)
            resultats['information_mutuelle'] = resultats_mutuelle
            
            # 4. ÉQUATION 4 : ENTROPIE CROISÉE
            print("   📊 Équation 4 : Entropie croisée...")
            resultats_croisee = self.analyser_entropie_croisee_tous_indices(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_croisee:
                    resultats[indice]['croisee'] = resultats_croisee[indice]
            
            # 5. ÉQUATION 5 : DIVERGENCE KL
            print("   📊 Équation 5 : Divergence KL...")
            resultats_kl = self.analyser_divergence_kl_comparative(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_kl:
                    resultats[indice]['divergence_kl'] = resultats_kl[indice]

            # 6. ÉQUATION 6 : ENTROPIE TEMPORELLE
            print("   📊 Équation 6 : Entropie temporelle...")
            resultats_temporelle = self.analyse_temporelle_multi_indices(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_temporelle:
                    resultats[indice]['temporelle'] = resultats_temporelle[indice]

            # 7. ÉQUATION 7 : ENTROPIE DE TRANSITION
            print("   📊 Équation 7 : Entropie de transition...")
            resultats_transition = self.analyse_transitions_tous_indices(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_transition:
                    resultats[indice]['transition'] = resultats_transition[indice]

            # 8. ÉQUATION 8 : ENTROPIE MULTI-ÉCHELLE
            print("   📊 Équation 8 : Entropie multi-échelle...")
            resultats_multi_echelle = self.analyse_entropie_multi_echelle(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_multi_echelle:
                    resultats[indice]['multi_echelle'] = resultats_multi_echelle[indice]
            
            # 9. ÉQUATION 9 : ENTROPIE JOINTE COMPLÈTE
            print("   📊 Équation 9 : Entropie jointe complète...")
            resultats_jointe = self.analyser_entropie_jointe_complete(composants_index5)
            resultats['entropie_jointe'] = resultats_jointe
            
            # 10. ÉQUATION 10 : ENTROPIE RELATIVE
            print("   📊 Équation 10 : Entropie relative...")
            resultats_relative = self.analyser_entropie_relative_tous_indices(sequences)
            for indice in ['INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3']:
                if indice in resultats_relative:
                    resultats[indice]['relative'] = resultats_relative[indice]
            
            print("   ✅ Analyse entropique avancée terminée")
            
        except Exception as e:
            print(f"   ❌ Erreur dans l'analyse entropique avancée : {e}")
            resultats['erreur'] = str(e)
        
        return resultats
    
    def analyser_entropie_renyi_tous_indices(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse Rényi pour INDEX5, INDEX2_INDEX3, INDEX1_INDEX3

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats Rényi pour chaque indice
        """
        resultats = {}

        # INDEX5 (18 valeurs) - Toujours présent
        if 'INDEX5' in sequences:
            index5_data = nettoyer_marqueurs(sequences['INDEX5'])
            resultats['INDEX5'] = {
                'diversite_alpha0': self._entropie_renyi(index5_data, alpha=0),      # Compte les combinaisons
                'shannon_alpha1': self._entropie_renyi(index5_data, alpha=1),        # Entropie classique
                'dominance_alpha2': self._entropie_renyi(index5_data, alpha=2),      # Détecte la dominance
                'monopole_alpha_inf': self._entropie_renyi(index5_data, alpha=float('inf'))  # État dominant
            }

        # INDEX2_INDEX3 (9 valeurs) - Optionnel
        if 'INDEX2_INDEX3' in sequences:
            index2_index3_data = nettoyer_marqueurs(sequences['INDEX2_INDEX3'])
            resultats['INDEX2_INDEX3'] = {
                'diversite_alpha0': self._entropie_renyi(index2_index3_data, alpha=0),
                'shannon_alpha1': self._entropie_renyi(index2_index3_data, alpha=1),
                'dominance_alpha2': self._entropie_renyi(index2_index3_data, alpha=2),
                'monopole_alpha_inf': self._entropie_renyi(index2_index3_data, alpha=float('inf'))
            }

        # INDEX1_INDEX3 (6 valeurs) - Optionnel
        if 'INDEX1_INDEX3' in sequences:
            index1_index3_data = nettoyer_marqueurs(sequences['INDEX1_INDEX3'])
            resultats['INDEX1_INDEX3'] = {
                'diversite_alpha0': self._entropie_renyi(index1_index3_data, alpha=0),
                'shannon_alpha1': self._entropie_renyi(index1_index3_data, alpha=1),
                'dominance_alpha2': self._entropie_renyi(index1_index3_data, alpha=2),
                'monopole_alpha_inf': self._entropie_renyi(index1_index3_data, alpha=float('inf'))
            }

        return resultats
    
    def _entropie_renyi(self, data: List[str], alpha: float) -> float:
        """
        Calcule l'entropie de Rényi d'ordre alpha
        
        Args:
            data: Données à analyser
            alpha: Ordre de l'entropie (0, 1, 2, inf)
            
        Returns:
            float: Entropie de Rényi en bits
        """
        if not data:
            return 0.0
        
        # Calculer les probabilités
        counter = Counter(data)
        n = len(data)
        probs = [count / n for count in counter.values()]
        
        if alpha == 0:
            # Entropie d'ordre 0 : log du nombre d'états
            return math.log2(len(probs))
        elif alpha == 1:
            # Entropie de Shannon (limite quand alpha -> 1)
            return -sum(p * math.log2(p) for p in probs if p > 0)
        elif alpha == float('inf'):
            # Entropie min : -log de la probabilité max
            return -math.log2(max(probs))
        else:
            # Entropie de Rényi générale
            if alpha == 2:
                # Optimisation pour alpha=2
                sum_p_alpha = sum(p * p for p in probs)
            else:
                sum_p_alpha = sum(p ** alpha for p in probs)
            
            if sum_p_alpha <= 0:
                return 0.0
            
            return (1 / (1 - alpha)) * math.log2(sum_p_alpha)
    
    def analyser_entropie_conditionnelle_prediction(self, composants_index5: Dict[str, List[str]]) -> Dict:
        """
        Analyse conditionnelle pour optimiser la prédiction de INDEX3
        
        Args:
            composants_index5: Composants extraits d'INDEX5 (INDEX1, INDEX2, INDEX3)
            
        Returns:
            dict: Résultats de l'analyse conditionnelle
        """
        if not composants_index5:
            return {
                'predicteurs': {},
                'meilleur_predicteur': None,
                'score_prediction': {},
                'erreur': 'Composants INDEX5 manquants'
            }
        
        index1_data = composants_index5.get('INDEX1', [])
        index2_data = composants_index5.get('INDEX2', [])
        index3_data = composants_index5.get('INDEX3', [])
        
        # Vérifier que les données ne sont pas vides
        if not index1_data or not index2_data or not index3_data:
            return {
                'predicteurs': {},
                'meilleur_predicteur': None,
                'score_prediction': {},
                'erreur': 'Données insuffisantes pour l\'analyse conditionnelle'
            }
        
        resultats = {
            'predicteurs': {},
            'score_prediction': {}
        }
        
        # H(INDEX3|INDEX1) : Prédire INDEX3 avec INDEX1
        h_index3 = self._shannon_entropy(index3_data)
        h_index3_given_index1 = self._entropie_conditionnelle(index3_data, index1_data)
        gain_index1 = h_index3 - h_index3_given_index1
        
        resultats['predicteurs']['INDEX1'] = {
            'entropie_cible': h_index3,
            'entropie_conditionnelle': h_index3_given_index1,
            'gain_information': gain_index1,
            'reduction_incertitude_pct': (gain_index1 / h_index3 * 100) if h_index3 > 0 else 0
        }
        
        # H(INDEX3|INDEX2) : Prédire INDEX3 avec INDEX2
        h_index3_given_index2 = self._entropie_conditionnelle(index3_data, index2_data)
        gain_index2 = h_index3 - h_index3_given_index2
        
        resultats['predicteurs']['INDEX2'] = {
            'entropie_cible': h_index3,
            'entropie_conditionnelle': h_index3_given_index2,
            'gain_information': gain_index2,
            'reduction_incertitude_pct': (gain_index2 / h_index3 * 100) if h_index3 > 0 else 0
        }
        
        # H(INDEX3|INDEX1,INDEX2) : Prédire INDEX3 avec INDEX1 ET INDEX2
        combined_predictors = list(zip(index1_data, index2_data))
        h_index3_given_combined = self._entropie_conditionnelle(index3_data, combined_predictors)
        gain_combined = h_index3 - h_index3_given_combined
        
        resultats['predicteurs']['INDEX1_INDEX2'] = {
            'entropie_cible': h_index3,
            'entropie_conditionnelle': h_index3_given_combined,
            'gain_information': gain_combined,
            'reduction_incertitude_pct': (gain_combined / h_index3 * 100) if h_index3 > 0 else 0
        }
        
        # Déterminer le meilleur prédicteur
        gains = {
            'INDEX1': gain_index1,
            'INDEX2': gain_index2,
            'INDEX1_INDEX2': gain_combined
        }
        
        meilleur_predicteur = max(gains, key=gains.get)
        resultats['meilleur_predicteur'] = meilleur_predicteur
        resultats['score_prediction'] = {
            'meilleur_gain': gains[meilleur_predicteur],
            'classement': sorted(gains.items(), key=lambda x: x[1], reverse=True)
        }
        
        return resultats

    def analyser_information_mutuelle_complete(self, sequences: Dict[str, List[str]],
                                             composants_index5: Dict[str, List[str]]) -> Dict:
        """
        Analyse complète de l'information mutuelle entre tous les indices

        Args:
            sequences: Dictionnaire des séquences
            composants_index5: Composants extraits d'INDEX5

        Returns:
            dict: Résultats de l'information mutuelle
        """
        if not composants_index5:
            return {'erreur': 'Composants INDEX5 manquants'}

        index1_data = composants_index5.get('INDEX1', [])
        index2_data = composants_index5.get('INDEX2', [])
        index3_data = composants_index5.get('INDEX3', [])

        resultats = {
            'paires': {},
            'combinaisons': {},
            'dependances': {}
        }

        # Information mutuelle par paires
        resultats['paires']['I_INDEX1_INDEX2'] = self._information_mutuelle(index1_data, index2_data)
        resultats['paires']['I_INDEX1_INDEX3'] = self._information_mutuelle(index1_data, index3_data)
        resultats['paires']['I_INDEX2_INDEX3'] = self._information_mutuelle(index2_data, index3_data)

        # Information mutuelle pour les combinaisons
        index2_index3_data = nettoyer_marqueurs(sequences['INDEX2_INDEX3'])
        index1_index3_data = nettoyer_marqueurs(sequences['INDEX1_INDEX3'])

        resultats['combinaisons'] = {
            'I_INDEX1_INDEX2INDEX3': self._information_mutuelle(index1_data, index2_index3_data),
            'I_INDEX2_INDEX1INDEX3': self._information_mutuelle(index2_data, index1_index3_data)
        }

        # Analyse des dépendances
        seuil_dependance = 0.1  # bits
        for nom, valeur in resultats['paires'].items():
            if valeur > seuil_dependance:
                resultats['dependances'][nom] = {
                    'valeur': valeur,
                    'niveau': 'forte' if valeur > 0.5 else 'moyenne' if valeur > 0.2 else 'faible',
                    'significative': True
                }
            else:
                resultats['dependances'][nom] = {
                    'valeur': valeur,
                    'niveau': 'negligeable',
                    'significative': False
                }

        return resultats

    def analyser_entropie_croisee_tous_indices(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse entropie croisée pour détecter les déviations du hasard

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de l'entropie croisée pour chaque indice
        """
        resultats = {}

        # Distributions théoriques uniformes
        distributions_uniformes = {
            'INDEX5': [1/18] * 18,
            'INDEX2_INDEX3': [1/9] * 9,
            'INDEX1_INDEX3': [1/6] * 6
        }

        for nom_index, sequence in sequences.items():
            if nom_index not in distributions_uniformes:
                continue

            # Nettoyer la séquence
            data_nettoyee = nettoyer_marqueurs(sequence)

            if not data_nettoyee:
                resultats[nom_index] = {
                    'entropie_croisee': 0,
                    'deviation_hasard': 0,
                    'classification': 'DONNEES_INSUFFISANTES'
                }
                continue

            # Calculer la distribution observée
            distribution_observee = self._calculer_distribution(data_nettoyee, len(distributions_uniformes[nom_index]))
            distribution_uniforme = distributions_uniformes[nom_index]

            # Entropie croisée H(p,q) = -Σ p(x) log q(x)
            entropie_croisee = 0
            for i, p_obs in enumerate(distribution_observee):
                if p_obs > 0 and distribution_uniforme[i] > 0:
                    entropie_croisee -= p_obs * math.log2(distribution_uniforme[i])

            # Entropie de Shannon de la distribution observée
            entropie_shannon = self._shannon_entropy_from_distribution(distribution_observee)

            # Déviation du hasard = H(p,q) - H(p)
            deviation_hasard = entropie_croisee - entropie_shannon

            # Classification
            if deviation_hasard < 0.1:
                classification = 'PROCHE_HASARD'
            elif deviation_hasard < 0.3:
                classification = 'DEVIATION_LEGERE'
            elif deviation_hasard < 0.5:
                classification = 'DEVIATION_MODEREE'
            else:
                classification = 'DEVIATION_FORTE'

            resultats[nom_index] = {
                'entropie_croisee': entropie_croisee,
                'entropie_shannon': entropie_shannon,
                'deviation_hasard': deviation_hasard,
                'classification': classification,
                'distribution_observee': distribution_observee,
                'distribution_uniforme': distribution_uniforme
            }

        return resultats

    def analyser_divergence_kl_comparative(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse divergence KL comparative pour détecter les anomalies

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de la divergence KL pour chaque indice
        """
        resultats = {}

        # Profils de référence uniformes
        profils_reference = {
            'INDEX5': np.ones(18) / 18,
            'INDEX2_INDEX3': np.ones(9) / 9,
            'INDEX1_INDEX3': np.ones(6) / 6
        }

        for nom_index, sequence in sequences.items():
            if nom_index not in profils_reference:
                continue

            # Nettoyer la séquence
            data_nettoyee = nettoyer_marqueurs(sequence)

            if not data_nettoyee:
                resultats[nom_index] = {
                    'divergence_kl': 0,
                    'classification': 'DONNEES_INSUFFISANTES'
                }
                continue

            # Calculer la distribution observée
            nb_categories = len(profils_reference[nom_index])
            dist_observee = self._calculer_distribution(data_nettoyee, nb_categories)

            # Calculer la divergence KL
            div_kl = self._kl_divergence(dist_observee, profils_reference[nom_index])

            # Classification selon la divergence
            classification = self._classifier_selon_divergence(div_kl, nom_index)

            resultats[nom_index] = {
                'divergence_kl': div_kl,
                'classification': classification,
                'distribution_observee': dist_observee.tolist(),
                'distribution_reference': profils_reference[nom_index].tolist()
            }

        return resultats

    def analyse_temporelle_multi_indices(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse temporelle multi-indices (ÉQUATION 6)

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de l'analyse temporelle
        """
        resultats = {}

        for nom_index, sequence in sequences.items():
            data_nettoyee = nettoyer_marqueurs(sequence)

            if len(data_nettoyee) < 10:
                resultats[nom_index] = {
                    'entropie_temporelle': 0,
                    'tendance': 'DONNEES_INSUFFISANTES'
                }
                continue

            # Diviser en segments temporels
            nb_segments = min(5, len(data_nettoyee) // 10)
            taille_segment = len(data_nettoyee) // nb_segments

            entropies_segments = []
            for i in range(nb_segments):
                debut = i * taille_segment
                fin = debut + taille_segment if i < nb_segments - 1 else len(data_nettoyee)
                segment = data_nettoyee[debut:fin]

                if segment:
                    entropie_segment = self._shannon_entropy(segment)
                    entropies_segments.append(entropie_segment)

            # Calculer l'entropie temporelle moyenne
            entropie_temporelle = np.mean(entropies_segments) if entropies_segments else 0

            # Détecter la tendance
            if len(entropies_segments) >= 3:
                # Régression linéaire simple
                x = np.arange(len(entropies_segments))
                y = np.array(entropies_segments)

                if len(x) > 1 and np.var(x) > 0:
                    coeff = np.corrcoef(x, y)[0, 1] if not np.isnan(np.corrcoef(x, y)[0, 1]) else 0

                    if coeff > 0.3:
                        tendance = 'CROISSANTE'
                    elif coeff < -0.3:
                        tendance = 'DECROISSANTE'
                    else:
                        tendance = 'STABLE'
                else:
                    tendance = 'STABLE'
            else:
                tendance = 'INDETERMINEE'

            resultats[nom_index] = {
                'entropie_temporelle': entropie_temporelle,
                'entropies_segments': entropies_segments,
                'tendance': tendance,
                'nb_segments': nb_segments
            }

        return resultats

    def analyse_transitions_tous_indices(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse des transitions pour tous les indices (ÉQUATION 7)

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de l'analyse des transitions
        """
        resultats = {}

        for nom_index, sequence in sequences.items():
            data_nettoyee = nettoyer_marqueurs(sequence)

            if len(data_nettoyee) < 2:
                resultats[nom_index] = {
                    'entropie_transitions': 0,
                    'predictibilite': 'DONNEES_INSUFFISANTES'
                }
                continue

            # Construire la matrice de transitions
            etats_uniques = sorted(set(data_nettoyee))
            n_etats = len(etats_uniques)

            if n_etats < 2:
                resultats[nom_index] = {
                    'entropie_transitions': 0,
                    'predictibilite': 'ETAT_UNIQUE'
                }
                continue

            # Compter les transitions
            transitions = {}
            for i in range(len(data_nettoyee) - 1):
                etat_actuel = data_nettoyee[i]
                etat_suivant = data_nettoyee[i + 1]

                if etat_actuel not in transitions:
                    transitions[etat_actuel] = {}

                if etat_suivant not in transitions[etat_actuel]:
                    transitions[etat_actuel][etat_suivant] = 0

                transitions[etat_actuel][etat_suivant] += 1

            # Calculer l'entropie de chaque état
            entropies_etats = []
            for etat in etats_uniques:
                if etat in transitions:
                    total_transitions = sum(transitions[etat].values())
                    if total_transitions > 0:
                        probs = [count / total_transitions for count in transitions[etat].values()]
                        entropie_etat = -sum(p * math.log2(p) for p in probs if p > 0)
                        entropies_etats.append(entropie_etat)

            # Entropie moyenne des transitions
            entropie_transitions = np.mean(entropies_etats) if entropies_etats else 0

            # Classification de la prédictibilité
            if entropie_transitions < 0.5:
                predictibilite = 'TRES_PREDICTIBLE'
            elif entropie_transitions < 1.0:
                predictibilite = 'MODEREMENT_PREDICTIBLE'
            elif entropie_transitions < 1.5:
                predictibilite = 'PEU_PREDICTIBLE'
            else:
                predictibilite = 'IMPREDICTIBLE'

            resultats[nom_index] = {
                'entropie_transitions': entropie_transitions,
                'entropies_etats': entropies_etats,
                'predictibilite': predictibilite,
                'nb_etats': n_etats
            }

        return resultats

    def analyse_entropie_multi_echelle(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse entropie multi-échelle (ÉQUATION 8)

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de l'analyse multi-échelle
        """
        resultats = {}

        for nom_index, sequence in sequences.items():
            data_nettoyee = nettoyer_marqueurs(sequence)

            if len(data_nettoyee) < 20:
                resultats[nom_index] = {
                    'entropies_echelles': [],
                    'complexite_moyenne': 0
                }
                continue

            # Analyser à différentes échelles
            echelles = [1, 2, 3, 5, 10]
            entropies_echelles = []

            for echelle in echelles:
                if len(data_nettoyee) >= echelle * 2:
                    # Sous-échantillonner
                    data_echelle = data_nettoyee[::echelle]

                    if len(data_echelle) >= 2:
                        entropie_echelle = self._shannon_entropy(data_echelle)
                        entropies_echelles.append({
                            'echelle': echelle,
                            'entropie': entropie_echelle,
                            'taille_echantillon': len(data_echelle)
                        })

            # Complexité moyenne
            complexite_moyenne = np.mean([e['entropie'] for e in entropies_echelles]) if entropies_echelles else 0

            resultats[nom_index] = {
                'entropies_echelles': entropies_echelles,
                'complexite_moyenne': complexite_moyenne,
                'nb_echelles_analysees': len(entropies_echelles)
            }

        return resultats

    def analyser_entropie_jointe_complete(self, composants_index5: Dict[str, List[str]]) -> Dict:
        """
        Analyse entropie jointe complète (ÉQUATION 9)

        Args:
            composants_index5: Composants extraits d'INDEX5

        Returns:
            dict: Résultats de l'analyse d'entropie jointe
        """
        if not composants_index5:
            return {'erreur': 'Composants INDEX5 manquants'}

        index1_data = composants_index5.get('INDEX1', [])
        index2_data = composants_index5.get('INDEX2', [])
        index3_data = composants_index5.get('INDEX3', [])

        if not all([index1_data, index2_data, index3_data]):
            return {'erreur': 'Données insuffisantes'}

        resultats = {}

        # Entropies individuelles avec validation de type
        h_index1 = self._shannon_entropy(index1_data)
        h_index2 = self._shannon_entropy(index2_data)
        h_index3 = self._shannon_entropy(index3_data)

        # Validation des types pour éviter l'erreur int + dict
        if not isinstance(h_index1, (int, float)):
            h_index1 = 0.0
        if not isinstance(h_index2, (int, float)):
            h_index2 = 0.0
        if not isinstance(h_index3, (int, float)):
            h_index3 = 0.0

        # Entropies jointes par paires avec validation de type
        h_index1_index2 = self._entropie_jointe(index1_data, index2_data)
        h_index1_index3 = self._entropie_jointe(index1_data, index3_data)
        h_index2_index3 = self._entropie_jointe(index2_data, index3_data)

        # Validation des types
        if not isinstance(h_index1_index2, (int, float)):
            h_index1_index2 = 0.0
        if not isinstance(h_index1_index3, (int, float)):
            h_index1_index3 = 0.0
        if not isinstance(h_index2_index3, (int, float)):
            h_index2_index3 = 0.0

        # Entropie jointe triple avec validation de type
        h_index1_index2_index3 = self._entropie_jointe_triple(index1_data, index2_data, index3_data)

        # Validation du type
        if not isinstance(h_index1_index2_index3, (int, float)):
            h_index1_index2_index3 = 0.0

        # Informations mutuelles avec validation des calculs
        try:
            i_index1_index2 = h_index1 + h_index2 - h_index1_index2
            i_index1_index3 = h_index1 + h_index3 - h_index1_index3
            i_index2_index3 = h_index2 + h_index3 - h_index2_index3
        except TypeError as e:
            print(f"      ⚠️ Erreur calcul information mutuelle : {e}")
            i_index1_index2 = 0.0
            i_index1_index3 = 0.0
            i_index2_index3 = 0.0

        # Information mutuelle triple avec validation
        try:
            i_triple = h_index1 + h_index2 + h_index3 - h_index1_index2 - h_index1_index3 - h_index2_index3 + h_index1_index2_index3
        except TypeError as e:
            print(f"      ⚠️ Erreur calcul information mutuelle triple : {e}")
            i_triple = 0.0

        resultats = {
            'entropies_individuelles': {
                'H_INDEX1': h_index1,
                'H_INDEX2': h_index2,
                'H_INDEX3': h_index3
            },
            'entropies_jointes_paires': {
                'H_INDEX1_INDEX2': h_index1_index2,
                'H_INDEX1_INDEX3': h_index1_index3,
                'H_INDEX2_INDEX3': h_index2_index3
            },
            'entropie_jointe_triple': h_index1_index2_index3,
            'informations_mutuelles': {
                'I_INDEX1_INDEX2': i_index1_index2,
                'I_INDEX1_INDEX3': i_index1_index3,
                'I_INDEX2_INDEX3': i_index2_index3,
                'I_TRIPLE': i_triple
            },
            'redondance_totale': self._safe_arithmetic_operation(
                lambda: h_index1 + h_index2 + h_index3 - h_index1_index2_index3,
                "calcul redondance totale"
            ),
            'synergie': i_triple
        }

        return resultats

    def analyser_entropie_relative_tous_indices(self, sequences: Dict[str, List[str]]) -> Dict:
        """
        Analyse entropie relative pour tous les indices (ÉQUATION 10)

        Args:
            sequences: Dictionnaire des séquences

        Returns:
            dict: Résultats de l'analyse d'entropie relative
        """
        resultats = {}

        # Entropies maximales théoriques
        entropies_max = {
            'INDEX5': math.log2(18),      # 18 combinaisons possibles
            'INDEX2_INDEX3': math.log2(9), # 9 combinaisons possibles
            'INDEX1_INDEX3': math.log2(6)  # 6 combinaisons possibles
        }

        for nom_index, sequence in sequences.items():
            if nom_index not in entropies_max:
                continue

            data_nettoyee = nettoyer_marqueurs(sequence)

            if not data_nettoyee:
                resultats[nom_index] = {
                    'entropie_relative': 0,
                    'efficacite_entropique': 0,
                    'classification': 'DONNEES_INSUFFISANTES'
                }
                continue

            # Entropie observée
            entropie_observee = self._shannon_entropy(data_nettoyee)

            # Entropie maximale
            entropie_max = entropies_max[nom_index]

            # Entropie relative (efficacité entropique)
            entropie_relative = entropie_observee / entropie_max if entropie_max > 0 else 0

            # Classification
            if entropie_relative > 0.9:
                classification = 'TRES_ALEATOIRE'
            elif entropie_relative > 0.7:
                classification = 'MODEREMENT_ALEATOIRE'
            elif entropie_relative > 0.5:
                classification = 'PEU_ALEATOIRE'
            else:
                classification = 'TRES_STRUCTURE'

            resultats[nom_index] = {
                'entropie_observee': entropie_observee,
                'entropie_maximale': entropie_max,
                'entropie_relative': entropie_relative,
                'efficacite_entropique': entropie_relative * 100,
                'classification': classification
            }

        return resultats

    # ==========================================
    # MÉTHODES UTILITAIRES
    # ==========================================

    def _shannon_entropy(self, data: List[str]) -> float:
        """
        Calcule l'entropie de Shannon

        Args:
            data: Données à analyser

        Returns:
            float: Entropie de Shannon en bits
        """
        if not data:
            return 0.0

        counter = Counter(data)
        n = len(data)

        entropy = 0.0
        for count in counter.values():
            p = count / n
            if p > 0:
                entropy -= p * math.log2(p)

        return entropy

    def _shannon_entropy_from_distribution(self, distribution: List[float]) -> float:
        """
        Calcule l'entropie de Shannon à partir d'une distribution

        Args:
            distribution: Distribution de probabilités

        Returns:
            float: Entropie de Shannon en bits
        """
        entropy = 0.0
        for p in distribution:
            if p > 0:
                entropy -= p * math.log2(p)
        return entropy

    def _entropie_conditionnelle(self, y_data: List[str], x_data: List[str]) -> float:
        """
        Calcule l'entropie conditionnelle H(Y|X) avec formules exactes consolidées

        Args:
            y_data: Variable dépendante
            x_data: Variable conditionnelle

        Returns:
            float: Entropie conditionnelle en bits
        """
        try:
            # Utiliser les formules exactes consolidées
            from formules_mathematiques_exactes import entropie_conditionnelle_exacte
            return entropie_conditionnelle_exacte(y_data, x_data)
        except ImportError:
            # Fallback vers l'ancienne méthode
            if not y_data or not x_data or len(y_data) != len(x_data):
                return 0.0

            # Compter les occurrences conjointes
            joint_counts = Counter(zip(x_data, y_data))
            x_counts = Counter(x_data)

            n = len(y_data)
            h_conditional = 0.0

            # Pour chaque valeur de X
            for x_val, x_count in x_counts.items():
                p_x = x_count / n

                # Calculer H(Y|X=x)
                h_y_given_x = 0.0
                for (x_joint, y_joint), joint_count in joint_counts.items():
                    if x_joint == x_val:
                        p_y_given_x = joint_count / x_count
                        if p_y_given_x > 0:
                            h_y_given_x -= p_y_given_x * math.log2(p_y_given_x)

                h_conditional += p_x * h_y_given_x

            return h_conditional

    def _information_mutuelle(self, x_data: List[str], y_data: List[str]) -> float:
        """
        Calcule l'information mutuelle I(X;Y) avec formules exactes consolidées

        Args:
            x_data: Première variable
            y_data: Deuxième variable

        Returns:
            float: Information mutuelle en bits
        """
        try:
            # Utiliser les formules exactes consolidées
            from formules_mathematiques_exactes import information_mutuelle_exacte
            return information_mutuelle_exacte(x_data, y_data)
        except ImportError:
            # Fallback vers l'ancienne méthode
            if not x_data or not y_data or len(x_data) != len(y_data):
                return 0.0

            h_x = self._shannon_entropy(x_data)
            h_y = self._shannon_entropy(y_data)
            h_xy = self._entropie_jointe(x_data, y_data)

            return h_x + h_y - h_xy

    def _entropie_jointe(self, x_data: List[str], y_data: List[str]) -> float:
        """
        Calcule l'entropie jointe H(X,Y) avec formules exactes consolidées

        Args:
            x_data: Première variable
            y_data: Deuxième variable

        Returns:
            float: Entropie jointe en bits
        """
        try:
            # Utiliser les formules exactes consolidées
            from formules_mathematiques_exactes import entropie_jointe_exacte
            return entropie_jointe_exacte(x_data, y_data)
        except ImportError:
            # Fallback vers l'ancienne méthode
            if not x_data or not y_data or len(x_data) != len(y_data):
                return 0.0

            joint_data = [f"{x}_{y}" for x, y in zip(x_data, y_data)]
            return self._shannon_entropy(joint_data)

    def _entropie_jointe_triple(self, x_data: List[str], y_data: List[str], z_data: List[str]) -> float:
        """
        Calcule l'entropie jointe triple H(X,Y,Z)

        Args:
            x_data: Première variable
            y_data: Deuxième variable
            z_data: Troisième variable

        Returns:
            float: Entropie jointe triple en bits
        """
        if not all([x_data, y_data, z_data]) or not all(len(x_data) == len(d) for d in [y_data, z_data]):
            return 0.0

        joint_data = [f"{x}_{y}_{z}" for x, y, z in zip(x_data, y_data, z_data)]
        return self._shannon_entropy(joint_data)

    def _calculer_distribution(self, data: List[str], nb_categories: int) -> np.ndarray:
        """
        Calcule la distribution de probabilités

        Args:
            data: Données à analyser
            nb_categories: Nombre de catégories attendues

        Returns:
            np.ndarray: Distribution de probabilités
        """
        if not data:
            return np.zeros(nb_categories)

        counter = Counter(data)
        distribution = np.zeros(nb_categories)

        # Obtenir toutes les catégories possibles
        categories = sorted(set(data))

        for i, cat in enumerate(categories[:nb_categories]):
            distribution[i] = counter[cat] / len(data)

        return distribution

    def _kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """
        Calcule la divergence de Kullback-Leibler D(P||Q)

        Args:
            p: Distribution observée
            q: Distribution de référence

        Returns:
            float: Divergence KL en bits
        """
        # Éviter les divisions par zéro
        epsilon = 1e-10
        p = np.array(p) + epsilon
        q = np.array(q) + epsilon

        # Normaliser
        p = p / np.sum(p)
        q = q / np.sum(q)

        # Calculer la divergence KL
        kl_div = 0.0
        for i in range(len(p)):
            if p[i] > epsilon and q[i] > epsilon:
                kl_div += p[i] * math.log2(p[i] / q[i])

        return kl_div

    def _safe_arithmetic_operation(self, operation, operation_name: str):
        """
        Effectue une opération arithmétique de manière sécurisée

        Args:
            operation: Fonction lambda contenant l'opération
            operation_name: Nom de l'opération pour le debug

        Returns:
            float: Résultat de l'opération ou 0.0 en cas d'erreur
        """
        try:
            result = operation()
            if not isinstance(result, (int, float)):
                print(f"      ⚠️ Erreur {operation_name} : résultat non numérique ({type(result)})")
                return 0.0
            return float(result)
        except TypeError as e:
            print(f"      ⚠️ Erreur {operation_name} : {e}")
            return 0.0
        except Exception as e:
            print(f"      ⚠️ Erreur {operation_name} : {e}")
            return 0.0

    def _classifier_selon_divergence(self, divergence: float, nom_index: str) -> str:
        """
        Classifie selon la divergence KL

        Args:
            divergence: Valeur de divergence KL
            nom_index: Nom de l'index

        Returns:
            str: Classification
        """
        if divergence < 0.1:
            return 'NORMAL'
        elif divergence < 0.3:
            return 'DEVIATION_LEGERE'
        elif divergence < 0.5:
            return 'DEVIATION_MODEREE'
        else:
            return 'ANOMALIE_FORTE'


# Fonctions d'export pour compatibilité
__all__ = [
    'AdvancedEntropy'
]
