"""
TEST LOGIQUE CORRECTE - EXCLUSION FENÊTRES AVEC TIE
===================================================

Test pour vérifier que la nouvelle logique d'exclusion des fenêtres
contenant des TIE fonctionne correctement.

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0 - Logique corrigée
"""

import sys
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def test_logique_fenetre_tie():
    """Test la nouvelle logique d'exclusion par fenêtres"""
    print("🔍 TEST LOGIQUE CORRECTE - EXCLUSION FENÊTRES AVEC TIE")
    print("=" * 70)
    
    # Séquence de test avec TIE à des positions spécifiques
    sequence_test = [
        'SYNC_pair_4_PLAYER',      # [0]
        'DESYNC_pair_6_BANKER',    # [1]  
        'SYNC_pair_4_TIE',         # [2] ← TIE
        'SYNC_impair_5_BANKER',    # [3]
        'DESYNC_impair_5_PLAYER',  # [4]
        'SYNC_pair_4_PLAYER',      # [5]
        'DESYNC_pair_6_BANKER',    # [6]
        'SYNC_impair_5_BANKER',    # [7]
        'DESYNC_impair_5_PLAYER'   # [8]
    ]
    
    print(f"📊 Séquence test : {len(sequence_test)} éléments")
    print(f"🚫 TIE présents : {sum(1 for x in sequence_test if 'TIE' in x)}")
    print(f"📍 Position TIE : index 2 (SYNC_pair_4_TIE)")
    
    # Analyser les fenêtres de 5 éléments
    ordre = 4  # Contexte de 4 + 1 suivant = 5 éléments
    fenetres_possibles = []
    fenetres_exclues = []
    fenetres_analysees = []
    
    for i in range(len(sequence_test) - ordre):
        fenetre = sequence_test[i:i+ordre+1]  # 5 éléments
        fenetres_possibles.append((i, fenetre))
        
        # Vérifier si fenêtre contient TIE
        contient_tie = any('TIE' in element for element in fenetre)
        
        if contient_tie:
            fenetres_exclues.append((i, fenetre))
        else:
            fenetres_analysees.append((i, fenetre))
    
    print(f"\n🔍 ANALYSE DES FENÊTRES DE 5 ÉLÉMENTS :")
    print("-" * 50)
    print(f"📈 Total fenêtres possibles : {len(fenetres_possibles)}")
    print(f"🚫 Fenêtres exclues (avec TIE) : {len(fenetres_exclues)}")
    print(f"✅ Fenêtres analysées : {len(fenetres_analysees)}")
    
    print(f"\n📋 DÉTAIL DES FENÊTRES EXCLUES :")
    for i, (pos, fenetre) in enumerate(fenetres_exclues, 1):
        print(f"   {i}. Position {pos} : {' → '.join(fenetre[:2])} → ... → {fenetre[-1]}")
        tie_positions = [j for j, elem in enumerate(fenetre) if 'TIE' in elem]
        print(f"      🚫 TIE à la position {tie_positions[0]} dans la fenêtre")
    
    print(f"\n✅ FENÊTRES ANALYSÉES (sans TIE) :")
    for i, (pos, fenetre) in enumerate(fenetres_analysees, 1):
        contexte = fenetre[:4]
        suivant = fenetre[4]
        print(f"   {i}. Position {pos} : {' → '.join(contexte[-2:])} → {suivant}")
    
    return len(fenetres_exclues), len(fenetres_analysees)

def test_avec_pattern_exploiter():
    """Test avec le PatternExploiter modifié"""
    print("\n🔍 TEST AVEC PATTERN EXPLOITER MODIFIÉ")
    print("=" * 70)
    
    try:
        from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
        
        # Séquence avec TIE stratégiquement placés
        sequence_test = [
            'SYNC_pair_4_PLAYER',      # [0]
            'DESYNC_pair_6_BANKER',    # [1]
            'SYNC_pair_4_TIE',         # [2] ← TIE (exclut fenêtres 0-4)
            'SYNC_impair_5_BANKER',    # [3]
            'DESYNC_impair_5_PLAYER',  # [4]
            'SYNC_pair_4_PLAYER',      # [5] ← Répétition pattern
            'DESYNC_pair_6_BANKER',    # [6]
            'SYNC_impair_5_BANKER',    # [7]
            'DESYNC_impair_5_PLAYER',  # [8]
            'SYNC_pair_4_PLAYER'       # [9]
        ]
        
        exploiter = PatternExploiter()
        exploiter.seuil_confiance = 0.10  # Très bas pour test
        exploiter.min_occurrences_markov = 1
        
        print(f"📊 Séquence test : {len(sequence_test)} éléments")
        print(f"🚫 TIE présents : {sum(1 for x in sequence_test if 'TIE' in x)}")
        
        # Analyser patterns avec nouvelle logique
        patterns = exploiter._extraire_patterns_markov_exploitables(sequence_test, {})
        
        print(f"\n📋 RÉSULTATS ANALYSE :")
        print(f"   🔗 Patterns détectés : {len(patterns)}")
        
        # Vérifier qu'aucun pattern ne provient d'une fenêtre avec TIE
        patterns_valides = 0
        for pattern in patterns:
            print(f"\n   Pattern {patterns_valides + 1} :")
            contexte = pattern['contexte']
            prediction = pattern['prediction']
            print(f"      Contexte : {' → '.join(contexte[-2:])}")
            print(f"      Prédiction : {prediction}")
            print(f"      Confiance : {pattern['confiance']:.1%}")
            
            # Vérifier qu'aucun élément ne contient TIE
            elements_tie = [elem for elem in contexte + [prediction] if 'TIE' in elem]
            if elements_tie:
                print(f"      ❌ ERREUR : Contient TIE : {elements_tie}")
            else:
                print(f"      ✅ Valide : Aucun TIE")
                patterns_valides += 1
        
        print(f"\n📊 RÉSUMÉ :")
        print(f"   ✅ Patterns valides : {patterns_valides}/{len(patterns)}")
        
        return patterns_valides == len(patterns)
        
    except Exception as e:
        print(f"❌ Erreur test : {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparaison_logiques():
    """Compare ancienne vs nouvelle logique"""
    print("\n🔍 COMPARAISON ANCIENNE VS NOUVELLE LOGIQUE")
    print("=" * 70)
    
    sequence_originale = [
        'SYNC_pair_4_PLAYER',      # [0]
        'DESYNC_pair_6_BANKER',    # [1]
        'SYNC_pair_4_TIE',         # [2] ← TIE
        'SYNC_impair_5_BANKER',    # [3]
        'DESYNC_impair_5_PLAYER'   # [4]
    ]
    
    print(f"📊 Séquence originale : {sequence_originale}")
    
    # ANCIENNE LOGIQUE (FAUSSE)
    print(f"\n❌ ANCIENNE LOGIQUE (FAUSSE) :")
    sequence_filtree = [x for x in sequence_originale if 'TIE' not in x]
    print(f"   1. Filtrer TIE : {sequence_filtree}")
    print(f"   2. Analyser fenêtres sur séquence filtrée")
    print(f"   3. Fenêtre possible : {sequence_filtree[0:4]} → {sequence_filtree[4] if len(sequence_filtree) > 4 else 'N/A'}")
    print(f"   ⚠️  PROBLÈME : Analyse des éléments [0,1,3,4] comme s'ils étaient consécutifs")
    
    # NOUVELLE LOGIQUE (CORRECTE)
    print(f"\n✅ NOUVELLE LOGIQUE (CORRECTE) :")
    print(f"   1. Garder séquence originale : {sequence_originale}")
    print(f"   2. Analyser fenêtres de 5 éléments consécutifs :")
    
    ordre = 4
    for i in range(len(sequence_originale) - ordre):
        fenetre = sequence_originale[i:i+ordre+1]
        contient_tie = any('TIE' in elem for elem in fenetre)
        statut = "EXCLUE" if contient_tie else "ANALYSÉE"
        print(f"      Fenêtre {i} : {fenetre} → {statut}")
    
    print(f"   ✅ AVANTAGE : Respect de la chronologie réelle")
    
    return True

def main():
    """Test principal de la logique corrigée"""
    print("🚫 TEST COMPLET - LOGIQUE CORRECTE EXCLUSION FENÊTRES TIE")
    print("=" * 80)
    print("Vérification que les fenêtres contenant des TIE sont exclues")
    print("=" * 80)
    
    tests_reussis = 0
    total_tests = 3
    
    # Test 1: Logique fenêtres
    try:
        nb_exclues, nb_analysees = test_logique_fenetre_tie()
        if nb_exclues > 0 and nb_analysees > 0:
            tests_reussis += 1
            print("✅ Test 1 réussi : Fenêtres correctement triées")
        else:
            print("❌ Test 1 échoué : Problème tri fenêtres")
    except Exception as e:
        print(f"❌ Test 1 erreur : {e}")
    
    # Test 2: PatternExploiter
    if test_avec_pattern_exploiter():
        tests_reussis += 1
        print("✅ Test 2 réussi : PatternExploiter fonctionne")
    else:
        print("❌ Test 2 échoué : Problème PatternExploiter")
    
    # Test 3: Comparaison logiques
    if test_comparaison_logiques():
        tests_reussis += 1
        print("✅ Test 3 réussi : Logiques comparées")
    else:
        print("❌ Test 3 échoué : Problème comparaison")
    
    # Résumé
    print("\n" + "=" * 80)
    print("📊 RÉSUMÉ TEST LOGIQUE CORRIGÉE")
    print("=" * 80)
    print(f"✅ Tests réussis : {tests_reussis}/{total_tests}")
    print(f"📈 Taux de réussite : {(tests_reussis/total_tests)*100:.1f}%")
    
    if tests_reussis == total_tests:
        print("\n🎉 LOGIQUE CORRECTE PARFAITEMENT IMPLÉMENTÉE !")
        print("✅ Fenêtres avec TIE correctement exclues")
        print("✅ Chronologie respectée")
        print("🎯 Patterns basés sur séquences réellement consécutives")
    else:
        print("\n⚠️  Logique incomplète")
        print("🔧 Vérifiez les corrections nécessaires")
    
    print("=" * 80)
    
    return tests_reussis == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
