#!/usr/bin/env python3
"""
Tests unitaires pour les corrections avancées du predictability_score

Auteur : Expert Statisticien
Date : 2025-06-20
"""

import unittest
import sys
import os

# Ajouter le chemin du projet
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer


class TestPredictabilityAdvanced(unittest.TestCase):
    """Tests pour les corrections avancées de prédictibilité"""
    
    def setUp(self):
        """Préparer les données de test"""
        # Données parfaitement prédictibles
        self.sequences_predictible = {
            'INDEX1': ['A'] * 100 + ['B'] * 100,
            'INDEX3': ['1'] * 100 + ['2'] * 100
        }
        
        # Données aléatoires
        import numpy as np
        np.random.seed(42)
        self.sequences_aleatoire = {
            'INDEX1': np.random.choice(['A', 'B'], 200).tolist(),
            'INDEX3': np.random.choice(['1', '2'], 200).tolist()
        }
        
        # Données avec relation partielle
        self.sequences_partielle = {
            'INDEX1': ['A'] * 50 + ['B'] * 50 + ['A'] * 50 + ['B'] * 50,
            'INDEX3': ['1'] * 40 + ['2'] * 10 + ['1'] * 10 + ['2'] * 40 + ['1'] * 30 + ['2'] * 20 + ['1'] * 20 + ['2'] * 30
        }
    
    def test_seuil_adaptatif(self):
        """Test du seuil adaptatif selon la taille d'échantillon"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test différentes tailles
        threshold_small = analyzer._calculate_adaptive_threshold(500)
        threshold_medium = analyzer._calculate_adaptive_threshold(5000)
        threshold_large = analyzer._calculate_adaptive_threshold(50000)
        
        # Vérifier que les seuils sont adaptatifs
        self.assertEqual(threshold_small['threshold'], 0.85)
        self.assertEqual(threshold_medium['threshold'], 0.75)
        self.assertEqual(threshold_large['threshold'], 0.65)
        
        # Vérifier les catégories
        self.assertEqual(threshold_small['category'], 'small')
        self.assertEqual(threshold_medium['category'], 'medium')
        self.assertEqual(threshold_large['category'], 'large')
    
    def test_iterations_adaptatives(self):
        """Test des formules d'itérations adaptatives"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test bootstrap
        bootstrap_small = analyzer._get_bootstrap_iterations(500)
        bootstrap_large = analyzer._get_bootstrap_iterations(50000)

        self.assertEqual(bootstrap_small, 100)  # max(100, 500 // 10) = max(100, 50) = 100
        self.assertEqual(bootstrap_large, 1000)  # min(1000, 50000 // 10)
        
        # Test permutations
        perm_small = analyzer._get_permutation_iterations(500)
        perm_large = analyzer._get_permutation_iterations(50000)
        
        self.assertEqual(perm_small, 100)  # max(100, 500 // 100)
        self.assertEqual(perm_large, 500)  # min(1000, 50000 // 100)
    
    def test_mesures_nonlineaires(self):
        """Test des mesures non-linéaires"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test avec données prédictibles
        measures = analyzer._calculate_nonlinear_measures(
            self.sequences_predictible['INDEX1'],
            self.sequences_predictible['INDEX3']
        )
        
        # Vérifier que les mesures sont calculées
        self.assertIn('cramer_v', measures)
        self.assertIn('spearman_correlation', measures)
        self.assertIn('mutual_info_normalized', measures)
        self.assertIn('composite_score', measures)
        
        # Pour des données parfaitement corrélées, Cramér V devrait être élevé
        self.assertGreater(measures['cramer_v'], 0.5)
    
    def test_correction_biais(self):
        """Test de la correction de biais"""
        analyzer = LupascoAnalyzer(self.sequences_partielle)
        
        # Test avec données partiellement corrélées
        correction = analyzer._correct_small_sample_bias(
            0.7,  # Score brut
            self.sequences_partielle['INDEX1'],
            self.sequences_partielle['INDEX3']
        )
        
        # Vérifier la structure du résultat
        self.assertIn('score_original', correction)
        self.assertIn('score_corrected', correction)
        self.assertIn('bias_estimate', correction)
        self.assertIn('confidence_interval_95', correction)
        
        # Vérifier que le score corrigé est dans [0, 1]
        self.assertGreaterEqual(correction['score_corrected'], 0)
        self.assertLessEqual(correction['score_corrected'], 1)
    
    def test_tests_statistiques(self):
        """Test des tests statistiques"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test avec données prédictibles
        tests = analyzer._perform_statistical_tests(
            self.sequences_predictible['INDEX1'],
            self.sequences_predictible['INDEX3'],
            1.0  # MI élevée
        )
        
        # Vérifier la structure
        self.assertIn('chi2_test', tests)
        self.assertIn('permutation_test', tests)
        self.assertIn('g_test', tests)
        self.assertIn('p_value', tests)
        self.assertIn('significant', tests)
        
        # Pour des données parfaitement corrélées, devrait être significatif
        self.assertLess(tests['p_value'], 0.05)
        self.assertTrue(tests['significant'])
    
    def test_integration_complete_avancee(self):
        """Test d'intégration complète avec mode avancé"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test avec mode avancé
        resultats = analyzer.analyser_predictibilite_index3_par_index1(use_advanced=True)
        
        # Vérifier que toutes les nouvelles métriques sont présentes
        self.assertIn('use_advanced', resultats)
        self.assertIn('seuil_adaptatif', resultats)
        self.assertIn('mesures_nonlineaires', resultats)
        self.assertIn('correction_biais', resultats)
        self.assertIn('tests_statistiques', resultats)
        self.assertIn('predictible_significatif', resultats)
        self.assertIn('methode', resultats)
        
        # Vérifier que la méthode est avancée
        self.assertTrue(resultats['use_advanced'])
        self.assertEqual(resultats['methode'], 'predictability_advanced_v1')
        
        # Pour des données parfaitement prédictibles, devrait être prédictible et significatif
        self.assertTrue(resultats['predictible'])
        self.assertTrue(resultats['predictible_significatif'])
    
    def test_integration_complete_traditionnelle(self):
        """Test d'intégration complète avec mode traditionnel"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test avec mode traditionnel
        resultats = analyzer.analyser_predictibilite_index3_par_index1(use_advanced=False)
        
        # Vérifier que les métriques avancées ne sont pas présentes
        self.assertFalse(resultats['use_advanced'])
        self.assertNotIn('mesures_nonlineaires', resultats)
        self.assertNotIn('correction_biais', resultats)
        self.assertNotIn('tests_statistiques', resultats)
        
        # Vérifier que la méthode est traditionnelle
        self.assertEqual(resultats['methode'], 'predictability_traditional')
        self.assertEqual(resultats['seuil_utilise'], 0.9)
    
    def test_compatibilite_descendante(self):
        """Test de la compatibilité descendante"""
        analyzer = LupascoAnalyzer(self.sequences_predictible)
        
        # Test sans paramètre (devrait utiliser mode avancé par défaut)
        resultats_defaut = analyzer.analyser_predictibilite_index3_par_index1()
        
        # Test avec paramètre explicite
        resultats_avance = analyzer.analyser_predictibilite_index3_par_index1(use_advanced=True)
        
        # Les résultats devraient être identiques
        self.assertEqual(resultats_defaut['use_advanced'], resultats_avance['use_advanced'])
        self.assertEqual(resultats_defaut['methode'], resultats_avance['methode'])
    
    def test_performance_gros_volumes(self):
        """Test de performance avec gros volumes (simulation)"""
        # Simuler un gros volume
        import numpy as np
        np.random.seed(42)
        
        sequences_gros = {
            'INDEX1': np.random.choice(['A', 'B', 'C'], 10000).tolist(),
            'INDEX3': np.random.choice(['1', '2', '3'], 10000).tolist()
        }
        
        analyzer = LupascoAnalyzer(sequences_gros)
        
        # Mesurer le temps d'exécution
        import time
        start_time = time.time()
        
        resultats = analyzer.analyser_predictibilite_index3_par_index1(use_advanced=True)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Vérifier que l'exécution est raisonnable (< 10 secondes)
        self.assertLess(execution_time, 10.0)
        
        # Vérifier que les résultats sont cohérents
        self.assertIn('taille_echantillon', resultats)
        self.assertEqual(resultats['taille_echantillon'], 10000)
        self.assertEqual(resultats['seuil_adaptatif'], 0.65)  # Seuil pour gros échantillon


if __name__ == '__main__':
    unittest.main()
