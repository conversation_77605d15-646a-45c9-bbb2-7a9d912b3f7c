"""
LUPASCO REFACTORED - ANALYSEUR RESTRUCTURÉ
==========================================

Module principal de l'analyseur Lupasco restructuré.
Architecture modulaire pour une meilleure maintenabilité.

Modules disponibles :
- core : Classes principales et logique de base
- utils : Utilitaires et fonctions d'aide
- statistics : Calculs statistiques et entropiques
- analyzers : Analyseurs spécialisés par INDEX
- reporting : Génération de rapports

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

__version__ = "2.0.0"
__author__ = "Système Lupasco Refactorisé"

# Imports principaux (seront ajoutés progressivement)
# from .core import *
# from .utils import *
# from .statistics import *
# from .analyzers import *
# from .reporting import *

__all__ = [
    # Sera complété au fur et à mesure de la migration
]
