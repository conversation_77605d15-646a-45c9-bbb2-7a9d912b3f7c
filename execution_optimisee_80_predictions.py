"""
EXÉCUTION OPTIMISÉE - 80 PRÉDICTIONS INDEX5
===========================================

Version optimisée pour gros fichiers (7GB) avec 80 prédictions.
Améliorations de performance et gestion mémoire.

Fichier cible : dataset_baccarat_lupasco_20250617_232800.json

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 2.0 - Optimisée
"""

import sys
import traceback
import gc
from datetime import datetime

# Ajouter chemins pour imports
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def charger_donnees_streaming_optimise():
    """Chargement optimisé avec streaming pour fichier 7GB"""
    fichier_json = "dataset_baccarat_lupasco_20250617_232800.json"
    
    try:
        print(f"📂 Chargement optimisé : {fichier_json}")
        
        # Vérifier la taille du fichier
        import os
        taille_fichier = os.path.getsize(fichier_json)
        taille_gb = taille_fichier / (1024 * 1024 * 1024)
        print(f"📁 Taille du fichier : {taille_gb:.2f} GB")
        
        # Utiliser le DataLoader avec optimisations
        from lupasco_refactored.core.data_loader import DataLoader
        
        # Nettoyage mémoire préalable
        gc.collect()
        
        loader = DataLoader(fichier_json)
        
        print("🔄 Chargement streaming optimisé...")
        resultats = loader.charger_donnees(force_streaming=True)
        
        print(f"✅ Chargement terminé avec succès")
        print(f"📊 Parties : {resultats.get('nb_parties_total', 'N/A'):,}")
        print(f"📈 Mains : {resultats.get('total_mains', 'N/A'):,}")
        
        # Nettoyage mémoire post-chargement
        gc.collect()
        
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur chargement : {e}")
        traceback.print_exc()
        return None

def executer_analyse_complete_optimisee(sequence_index5):
    """Exécution optimisée de l'analyse complète avec 80 prédictions"""
    print("\n" + "="*80)
    print("🎯 ANALYSE COMPLÈTE OPTIMISÉE - 80 PRÉDICTIONS")
    print("="*80)
    
    try:
        from lupasco_refactored.analyzers.index5_master_analyzer import Index5MasterAnalyzer
        
        master = Index5MasterAnalyzer()
        nb_predictions = 80
        
        print(f"🎯 Analyse sur {len(sequence_index5):,} éléments")
        print(f"🔮 Génération de {nb_predictions} prédictions optimisées")
        print("⚡ Mode haute performance activé")
        
        # Nettoyage mémoire avant analyse
        gc.collect()
        
        # Analyse avec optimisations
        resultats = master.analyser_index5_complet(
            sequence_index5,
            nb_predictions=nb_predictions,
            generer_rapport=True
        )
        
        if 'erreur' in resultats:
            print(f"❌ Erreur : {resultats['erreur']}")
            return None
        
        # Affichage résultats optimisé
        print("\n🎯 RÉSULTATS ANALYSE COMPLÈTE :")
        print("-" * 50)
        
        # Synthèse globale
        if 'synthese_globale' in resultats:
            synthese = resultats['synthese_globale']
            
            # Scores
            scores = synthese.get('scores_globaux', {})
            print(f"📊 Score global : {scores.get('global', 'N/A'):.1f}/100")
            print(f"🔬 Score complexité : {scores.get('complexite', 'N/A')}/100")
            print(f"🚨 Score anomalies : {scores.get('anomalies', 'N/A'):.1f}/100")
            print(f"🔮 Score prédiction : {scores.get('prediction', 'N/A'):.1f}/100")
            
            # Classifications
            print(f"📈 Classification : {synthese.get('classification_index5', 'N/A')}")
            print(f"⚠️  Niveau risque : {synthese.get('niveau_risque', 'N/A')}")
            
            # Métriques
            metriques = synthese.get('metriques_cles', {})
            print(f"🔢 Anomalies : {metriques.get('nb_anomalies_detectees', 'N/A')}")
            print(f"📋 Recommandations : {metriques.get('nb_recommandations', 'N/A')}")
        
        # Prédictions (top 15 sur 80)
        if 'prediction_adaptative' in resultats:
            pred_data = resultats['prediction_adaptative']
            predictions = pred_data.get('predictions', [])
            confiances = pred_data.get('confiance_predictions', [])
            
            if predictions:
                print(f"\n🔮 PRÉDICTIONS GÉNÉRÉES (top 15 sur {len(predictions)}) :")
                for i, (pred, conf) in enumerate(zip(predictions[:15], confiances[:15]), 1):
                    print(f"   {i:2d}. {pred} (confiance: {conf:.3f})")
                
                # Statistiques prédictions
                if confiances:
                    conf_moy = sum(confiances) / len(confiances)
                    conf_max = max(confiances)
                    conf_min = min(confiances)
                    print(f"\n📊 STATISTIQUES PRÉDICTIONS :")
                    print(f"   📈 Confiance moyenne : {conf_moy:.3f}")
                    print(f"   🎯 Confiance maximale : {conf_max:.3f}")
                    print(f"   📉 Confiance minimale : {conf_min:.3f}")
        
        # Recommandations prioritaires
        if 'synthese_globale' in resultats:
            reco = resultats['synthese_globale'].get('recommandations_prioritaires', [])
            if reco:
                print(f"\n📋 RECOMMANDATIONS PRIORITAIRES (top 10) :")
                for i, rec in enumerate(reco[:10], 1):
                    print(f"   {i:2d}. {rec}")
        
        # Sauvegarde optimisée
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        nom_json = f"analyse_80_predictions_{timestamp}.json"
        nom_rapport = f"rapport_80_predictions_{timestamp}.txt"
        
        print(f"\n💾 SAUVEGARDE RÉSULTATS :")
        master.sauvegarder_resultats(resultats, nom_json)
        master.generer_rapport_texte(resultats, nom_rapport)
        
        print(f"   📄 JSON : {nom_json}")
        print(f"   📋 Rapport : {nom_rapport}")
        
        # Nettoyage mémoire final
        gc.collect()
        
        print("✅ Analyse complète terminée avec succès")
        return resultats
        
    except Exception as e:
        print(f"❌ Erreur analyse complète : {e}")
        traceback.print_exc()
        return None

def afficher_resume_performance(sequence_index5, resultats):
    """Affiche un résumé de performance optimisé"""
    print("\n" + "="*80)
    print("📊 RÉSUMÉ PERFORMANCE - 80 PRÉDICTIONS")
    print("="*80)
    
    # Données analysées
    print(f"📈 Éléments analysés : {len(sequence_index5):,}")
    print(f"🔮 Prédictions générées : 80")
    
    # Ratio performance
    ratio = len(sequence_index5) / 80
    print(f"⚡ Ratio données/prédictions : {ratio:,.0f}:1")
    
    if resultats and 'synthese_globale' in resultats:
        synthese = resultats['synthese_globale']
        scores = synthese.get('scores_globaux', {})
        
        print(f"🎯 Score global final : {scores.get('global', 0):.1f}/100")
        print(f"📊 Classification système : {synthese.get('classification_index5', 'N/A')}")
        
        # Évaluation qualité
        score_global = scores.get('global', 0)
        if score_global >= 70:
            qualite = "EXCELLENTE ✨"
        elif score_global >= 50:
            qualite = "BONNE ✅"
        elif score_global >= 30:
            qualite = "CORRECTE ⚠️"
        else:
            qualite = "FAIBLE ❌"
        
        print(f"🏆 Qualité analyse : {qualite}")
    
    print("="*80)

def main():
    """Fonction principale optimisée"""
    print("🎓 EXÉCUTION OPTIMISÉE - 80 PRÉDICTIONS INDEX5")
    print("="*80)
    print("Expert Statisticien IA - Version haute performance")
    print("Fichier : dataset_baccarat_lupasco_20250617_232800.json")
    print(f"📅 Date : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 1. Chargement optimisé
    print("\n🚀 PHASE 1 : CHARGEMENT DONNÉES")
    resultats_chargement = charger_donnees_streaming_optimise()
    if not resultats_chargement:
        print("❌ Échec chargement")
        return False
    
    # 2. Extraction INDEX5
    print("\n🔍 PHASE 2 : EXTRACTION INDEX5")
    try:
        sequence_index5 = resultats_chargement['sequences']['INDEX5']
        sequence_index5_clean = [x for x in sequence_index5 if x != "__FIN_PARTIE__"]
        print(f"✅ INDEX5 extrait : {len(sequence_index5_clean):,} éléments")
    except Exception as e:
        print(f"❌ Erreur extraction : {e}")
        return False
    
    # 3. Analyse complète optimisée
    print("\n⚡ PHASE 3 : ANALYSE COMPLÈTE 80 PRÉDICTIONS")
    resultats = executer_analyse_complete_optimisee(sequence_index5_clean)
    
    # 4. Résumé performance
    afficher_resume_performance(sequence_index5_clean, resultats)
    
    # 5. Conclusion
    success = resultats is not None
    if success:
        print("🎉 EXÉCUTION OPTIMISÉE RÉUSSIE !")
        print("✅ 80 prédictions générées avec succès")
        print("📄 Fichiers de résultats sauvegardés")
    else:
        print("❌ Échec de l'exécution optimisée")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
