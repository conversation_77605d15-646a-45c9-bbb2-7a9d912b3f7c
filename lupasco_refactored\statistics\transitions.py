"""
STATISTICS - TRANSITION ANALYZER
=================================

Module contenant les analyses de transitions et cycles
pour l'analyseur Lupasco refactorisé.

Classe TransitionAnalyzer pour analyser les transitions entre combinaisons,
détecter les cycles et analyser les patterns temporels.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

import numpy as np
from typing import List, Dict, Tuple
from scipy.stats import chi2_contingency
from lupasco_refactored.utils.data_utils import diviser_en_parties, nettoyer_marqueurs


class TransitionAnalyzer:
    """
    Classe pour analyser les transitions entre combinaisons et détecter les cycles
    Supporte l'analyse des matrices de transition, détection de cycles et patterns temporels
    """
    
    def __init__(self):
        """
        Initialise TransitionAnalyzer
        """
        pass
    
    def analyser_transitions(self, sequence: List[str], nom_sequence: str = "SEQUENCE") -> Dict:
        """
        Analyse les transitions entre combinaisons avec formules exactes
        Respecte l'indépendance des parties
        
        Args:
            sequence: Séquence à analyser (peut contenir des marqueurs)
            nom_sequence: Nom de la séquence pour l'affichage
            
        Returns:
            dict: Résultats de l'analyse des transitions
        """
        print(f"   🔄 Calcul des matrices de transition {nom_sequence}...")
        
        # Diviser en parties indépendantes
        parties_sequences = diviser_en_parties(sequence)
        
        # Créer la matrice de transition
        sequence_sans_marqueurs = []
        for partie_seq in parties_sequences:
            sequence_sans_marqueurs.extend(partie_seq)
        
        combinaisons = sorted(set(sequence_sans_marqueurs))
        # Supprimer le marqueur s'il existe encore
        if "__FIN_PARTIE__" in combinaisons:
            combinaisons.remove("__FIN_PARTIE__")

        n_combos = len(combinaisons)

        # Cas particulier : pas assez de combinaisons
        if n_combos == 0:
            return {
                'matrice_transitions': [],
                'matrice_probabilities': [],
                'entropies_transitions': [],
                'gini_transitions': [],
                'test_independance': {
                    'chi2_statistic': 0,
                    'p_value': 1.0,
                    'degrees_freedom': 0,
                    'independant': True
                },
                'combinaisons_ordre': []
            }
        
        # Matrice de comptage des transitions
        matrice_transitions = np.zeros((n_combos, n_combos))
        
        # Analyser les transitions dans chaque partie séparément
        for partie_seq in parties_sequences:
            if len(partie_seq) < 2:
                continue
            
            for i in range(len(partie_seq) - 1):
                if partie_seq[i] in combinaisons and partie_seq[i + 1] in combinaisons:
                    from_idx = combinaisons.index(partie_seq[i])
                    to_idx = combinaisons.index(partie_seq[i + 1])
                    matrice_transitions[from_idx, to_idx] += 1
        
        # Convertir en probabilités
        matrice_probabilities = np.zeros_like(matrice_transitions)
        for i in range(n_combos):
            row_sum = np.sum(matrice_transitions[i, :])
            if row_sum > 0:
                matrice_probabilities[i, :] = matrice_transitions[i, :] / row_sum
        
        # Calculer l'entropie de chaque ligne (diversité des transitions)
        entropies_transitions = []
        for i in range(n_combos):
            probs = matrice_probabilities[i, :]
            probs = probs[probs > 0]  # Supprimer les zéros
            if len(probs) > 0:
                entropie = -np.sum(probs * np.log2(probs))
                entropies_transitions.append(entropie)
            else:
                entropies_transitions.append(0)
        
        # Test d'indépendance chi-carré avec vérification de sécurité renforcée
        total_transitions = np.sum(matrice_transitions)
        lignes_non_nulles = np.sum(np.sum(matrice_transitions, axis=1) > 0)
        cols_non_nulles = np.sum(np.sum(matrice_transitions, axis=0) > 0)

        # Conditions strictes pour le test Chi-carré
        conditions_chi2_ok = (
            total_transitions >= 10 and  # Au moins 10 transitions au total
            lignes_non_nulles >= 2 and   # Au moins 2 lignes non nulles
            cols_non_nulles >= 2 and     # Au moins 2 colonnes non nulles
            n_combos >= 2                # Au moins 2 combinaisons
        )

        if not conditions_chi2_ok:
            # Affichage silencieux pour les cas normaux (séquences vides, etc.)
            if total_transitions > 0:
                print(f"      ⚠️ Chi2 non applicable : transitions={total_transitions}, lignes={lignes_non_nulles}, cols={cols_non_nulles}")
            chi2_stat, p_value, dof = 0, 1.0, 0
        else:
            try:
                # Vérification supplémentaire : toutes les fréquences attendues > 0
                chi2_stat, p_value, dof, expected = chi2_contingency(matrice_transitions)

                # Vérifier que toutes les fréquences attendues sont > 0
                if np.any(expected <= 0):
                    print(f"      ⚠️ Chi2 non fiable : fréquences attendues nulles détectées")
                    chi2_stat, p_value, dof = 0, 1.0, 0

            except (ValueError, ZeroDivisionError) as e:
                print(f"      ⚠️ Chi2 test échoué : {e}")
                chi2_stat, p_value, dof = 0, 1.0, 0
        
        # Coefficient de Gini pour mesurer la concentration des transitions
        gini_transitions = []
        for i in range(n_combos):
            row = matrice_transitions[i, :]
            row_sum = np.sum(row)

            if row_sum > 0 and len(row) > 1:
                # Calcul simple du coefficient de Gini
                row_sorted = np.sort(row)
                n = len(row_sorted)

                # Éviter la division par zéro
                if n > 0 and row_sum > 0:
                    cumsum = np.cumsum(row_sorted)
                    if cumsum[-1] > 0:
                        gini = (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n
                        gini_transitions.append(max(0, min(1, gini)))  # Borner entre 0 et 1
                    else:
                        gini_transitions.append(0)
                else:
                    gini_transitions.append(0)
            else:
                gini_transitions.append(0)
        
        return {
            'matrice_transitions': matrice_transitions.tolist(),
            'matrice_probabilities': matrice_probabilities.tolist(),
            'entropies_transitions': entropies_transitions,
            'gini_transitions': gini_transitions,
            'test_independance': {
                'chi2_statistic': chi2_stat,
                'p_value': p_value,
                'degrees_freedom': dof,
                'independant': p_value > 0.05
            },
            'combinaisons_ordre': combinaisons
        }
    
    def detecter_cycles_avec_frontieres_precises(self, sequence: List[str], nom_index: str, 
                                               max_periode: int = 50, frontieres_info: Dict = None) -> Dict:
        """
        Détecte les cycles en tenant compte des frontières exactes entre parties
        
        Args:
            sequence: Séquence à analyser (peut contenir des marqueurs)
            nom_index: Nom de l'index pour le rapport
            max_periode: Période maximale à tester
            frontieres_info: Informations sur les frontières (optionnel)
            
        Returns:
            dict: Résultats avec classification de fiabilité des cycles
        """
        if frontieres_info and 'erreur' not in frontieres_info:
            # Nettoyer la séquence des marqueurs
            sequence_complete = [x for x in sequence if x != "__FIN_PARTIE__"]
            frontieres_positions = frontieres_info['frontieres_positions']
        else:
            # Fallback : utiliser la méthode de division par marqueurs
            parties_sequences = diviser_en_parties(sequence)
            sequence_complete = []
            for partie_seq in parties_sequences:
                sequence_complete.extend(partie_seq)
            frontieres_positions = []
            debut = 0
            for partie_seq in parties_sequences[:-1]:  # Exclure la dernière
                debut += len(partie_seq)
                frontieres_positions.append(debut - 1)
        
        # Convertir en séquence numérique
        combinaisons = sorted(set(sequence_complete))
        sequence_num = [combinaisons.index(x) for x in sequence_complete]
        
        # Calculer l'autocorrélation
        autocorr = self._calculer_autocorrelation_simple(sequence_num, max_lag=max_periode)
        
        # Détecter les cycles et classifier leur fiabilité
        cycles_detectes = []
        for lag in range(2, len(autocorr)):
            if autocorr[lag] > 0.1:  # Seuil de corrélation significative
                # Vérifier si ce cycle traverse des frontières
                cycles_traversant = 0
                for frontiere in frontieres_positions:
                    # Un cycle de période 'lag' traverse une frontière si :
                    # (position_frontiere + k) % lag == 0 pour un k dans [0, lag-1]
                    if any((frontiere + k) % lag == 0 for k in range(lag)):
                        cycles_traversant += 1
                
                # Classification de fiabilité
                if len(frontieres_positions) == 0:
                    fiabilite = 'haute'  # Une seule partie
                elif cycles_traversant == 0:
                    fiabilite = 'haute'
                elif cycles_traversant <= len(frontieres_positions) * 0.3:
                    fiabilite = 'moyenne'
                else:
                    fiabilite = 'faible'
                
                cycles_detectes.append({
                    'periode': lag,
                    'correlation': autocorr[lag],
                    'frontieres_traversees': cycles_traversant,
                    'fiabilite': fiabilite,
                    'pourcentage_frontieres': (cycles_traversant / len(frontieres_positions) * 100) if frontieres_positions else 0
                })
        
        # Trier par corrélation décroissante
        cycles_detectes.sort(key=lambda x: x['correlation'], reverse=True)
        
        return {
            'cycles_detectes': cycles_detectes[:10],  # Top 10
            'autocorrelation_complete': autocorr.tolist(),
            'nb_frontieres': len(frontieres_positions),
            'nb_parties_analysees': len(frontieres_positions) + 1,
            'methode': f'detection_cycles_avec_frontieres_precises_{nom_index}',
            'frontieres_positions': frontieres_positions
        }
    
    def _calculer_autocorrelation_simple(self, sequence_num: List[int], max_lag: int = 20) -> np.ndarray:
        """
        Calcule l'autocorrélation simple d'une séquence numérique

        Args:
            sequence_num: Séquence numérique
            max_lag: Nombre maximum de lags

        Returns:
            np.ndarray: Coefficients d'autocorrélation
        """
        n = len(sequence_num)
        autocorr = np.zeros(max_lag + 1)

        # Lag 0 = 1 par définition
        autocorr[0] = 1.0

        # Convertir en array numpy pour les calculs
        sequence_array = np.array(sequence_num)

        for lag in range(1, min(max_lag + 1, n // 2)):  # Augmenter la limite
            if lag >= n:
                break

            x1 = sequence_array[:-lag]
            x2 = sequence_array[lag:]

            if len(x1) > 1 and len(x2) > 1:
                # Vérifier la variance pour éviter les divisions par zéro
                var_x1 = np.var(x1)
                var_x2 = np.var(x2)

                if var_x1 > 0 and var_x2 > 0:
                    corr = np.corrcoef(x1, x2)[0, 1]
                    autocorr[lag] = corr if not np.isnan(corr) else 0
                else:
                    # Si variance nulle, calculer corrélation directement
                    if np.array_equal(x1, x2):
                        autocorr[lag] = 1.0
                    else:
                        autocorr[lag] = 0.0

        return autocorr
    
    def analyser_patterns_temporels(self, sequence: List[str], nom_index: str, 
                                  frontieres_info: Dict = None) -> Dict:
        """
        Analyse les patterns temporels dans une séquence
        
        Args:
            sequence: Séquence à analyser
            nom_index: Nom de l'index pour le rapport
            frontieres_info: Informations sur les frontières (optionnel)
            
        Returns:
            dict: Résultats de l'analyse des patterns temporels
        """
        print(f"   ⏰ Analyse des patterns temporels {nom_index}...")
        
        # 1. Analyse des cycles et périodicités
        cycles = self.detecter_cycles_avec_frontieres_precises(sequence, nom_index, frontieres_info=frontieres_info)
        
        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_avec_frontieres_precises(sequence, nom_index, frontieres_info)
        
        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_avec_frontieres_precises(sequence, nom_index, frontieres_info)
        
        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }
    
    def _tester_stationnarite_avec_frontieres_precises(self, sequence: List[str], nom_index: str, 
                                                     frontieres_info: Dict = None, nb_segments: int = 10) -> Dict:
        """
        Teste la stationnarité en respectant les frontières entre parties
        
        Args:
            sequence: Séquence à analyser
            nom_index: Nom de l'index pour le rapport
            frontieres_info: Informations sur les frontières
            nb_segments: Nombre de segments souhaités
            
        Returns:
            dict: Résultats avec analyses intra-parties et inter-parties
        """
        if frontieres_info and 'erreur' not in frontieres_info:
            # Diviser la séquence selon les frontières détectées
            sequence_complete = [x for x in sequence if x != "__FIN_PARTIE__"]
            parties_sequences = []
            debut = 0
            
            for frontiere_pos in frontieres_info['frontieres_positions']:
                partie_seq = sequence_complete[debut:frontiere_pos + 1]
                parties_sequences.append(partie_seq)
                debut = frontiere_pos + 1
            
            # Ajouter la dernière partie
            if debut < len(sequence_complete):
                partie_seq = sequence_complete[debut:]
                parties_sequences.append(partie_seq)
        else:
            # Fallback : utiliser la méthode de division par marqueurs
            parties_sequences = diviser_en_parties(sequence)
        
        # Analyse simplifiée de stationnarité
        return {
            'nb_parties_analysees': len(parties_sequences),
            'methode': f'test_stationnarite_avec_frontieres_precises_{nom_index}',
            'stationnaire': True  # Placeholder
        }
    
    def _analyser_tendances_avec_frontieres_precises(self, sequence: List[str], nom_index: str, 
                                                   frontieres_info: Dict = None) -> Dict:
        """
        Analyse les tendances en respectant les frontières entre parties
        
        Args:
            sequence: Séquence à analyser
            nom_index: Nom de l'index pour le rapport
            frontieres_info: Informations sur les frontières
            
        Returns:
            dict: Résultats de l'analyse des tendances
        """
        # Analyse simplifiée des tendances
        return {
            'methode': f'analyse_tendances_avec_frontieres_precises_{nom_index}',
            'tendance_detectee': False  # Placeholder
        }


# Fonctions d'export pour compatibilité
__all__ = [
    'TransitionAnalyzer'
]
