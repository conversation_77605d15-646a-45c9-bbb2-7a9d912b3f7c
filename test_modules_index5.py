"""
TEST COMPLET DES MODULES INDEX5 AVANCÉS
=======================================

Script de test pour vérifier le bon fonctionnement de tous les modules INDEX5
créés avec les données JSON réelles.

Auteur : Expert Statisticien IA
Date : 2025-06-20
Version : 1.0
"""

import json
import sys
import traceback
from typing import List, Dict

# Ajouter le chemin pour les imports
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def charger_donnees_json(nom_fichier: str = "dataset_test_3_parties_complet.json") -> Dict:
    """Charge les données JSON de test"""
    try:
        with open(nom_fichier, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Données JSON chargées : {nom_fichier}")
        return data
    except Exception as e:
        print(f"❌ Erreur chargement JSON : {e}")
        return {}

def extraire_sequence_index5(data: Dict) -> List[str]:
    """Extrait la séquence INDEX5 des données JSON"""
    try:
        sequence_index5 = []

        # Vérifier si on a la structure attendue avec 'parties'
        if 'parties' not in data:
            print("❌ Structure JSON invalide : clé 'parties' manquante")
            return []

        # Parcourir toutes les parties
        for partie in data['parties']:
            if 'mains' not in partie:
                continue

            # Parcourir toutes les mains de cette partie
            for main in partie['mains']:
                if 'index5_combined' in main:
                    index5_value = main['index5_combined']
                    if index5_value:  # Vérifier que ce n'est pas None ou vide
                        sequence_index5.append(str(index5_value))

        if len(sequence_index5) == 0:
            print("❌ Aucune valeur INDEX5 trouvée dans les mains")
            return []

        print(f"✅ Séquence INDEX5 extraite : {len(sequence_index5)} éléments")

        # Afficher quelques exemples pour vérification
        if len(sequence_index5) >= 5:
            print(f"   Exemples : {sequence_index5[:5]}")

        return sequence_index5

    except Exception as e:
        print(f"❌ Erreur extraction INDEX5 : {e}")
        traceback.print_exc()
        return []

def tester_module_entropy_avancee():
    """Test du module d'entropie avancée INDEX5"""
    print("\n" + "="*60)
    print("🔬 TEST MODULE : INDEX5 ADVANCED ENTROPY")
    print("="*60)
    
    try:
        from lupasco_refactored.statistics.index5_advanced_entropy import Index5AdvancedEntropy
        
        # Charger données
        data = charger_donnees_json()
        if not data:
            return False
        
        sequence_index5 = extraire_sequence_index5(data)
        if not sequence_index5:
            return False
        
        # Tester le module
        analyzer = Index5AdvancedEntropy()
        print(f"📊 Test avec {len(sequence_index5)} éléments INDEX5...")
        
        # Prendre un échantillon pour test rapide
        echantillon = sequence_index5[:1000] if len(sequence_index5) > 1000 else sequence_index5
        
        resultats = analyzer.analyser_index5_complet(echantillon)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur dans l'analyse : {resultats['erreur']}")
            return False
        
        # Vérifier les résultats
        sections_attendues = ['entropie_multi_echelle', 'markov_ordre_superieur', 
                             'analyse_spectrale', 'tests_stationnarite', 'synthese_index5']
        
        for section in sections_attendues:
            if section in resultats:
                print(f"✅ Section {section} : OK")
            else:
                print(f"❌ Section {section} : MANQUANTE")
        
        # Afficher quelques résultats clés
        if 'synthese_index5' in resultats:
            synthese = resultats['synthese_index5']
            print(f"📈 Score complexité : {synthese.get('score_complexite_globale', 'N/A')}")
            print(f"📊 Classification : {synthese.get('classification_index5', 'N/A')}")
        
        print("✅ Module INDEX5 Advanced Entropy : FONCTIONNEL")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test entropy avancée : {e}")
        traceback.print_exc()
        return False

def tester_module_anomaly_detection():
    """Test du module de détection d'anomalies INDEX5"""
    print("\n" + "="*60)
    print("🚨 TEST MODULE : INDEX5 ANOMALY DETECTION")
    print("="*60)
    
    try:
        from lupasco_refactored.statistics.index5_anomaly_detection import Index5AnomalyDetector
        
        # Charger données
        data = charger_donnees_json()
        if not data:
            return False
        
        sequence_index5 = extraire_sequence_index5(data)
        if not sequence_index5:
            return False
        
        # Tester le module
        detector = Index5AnomalyDetector()
        print(f"🔍 Test détection anomalies avec {len(sequence_index5)} éléments...")
        
        # Prendre un échantillon
        echantillon = sequence_index5[:1000] if len(sequence_index5) > 1000 else sequence_index5
        
        resultats = detector.detecter_anomalies_index5(echantillon)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur dans la détection : {resultats['erreur']}")
            return False
        
        # Vérifier les résultats
        print(f"📊 Score anomalie global : {resultats.get('score_anomalie_global', 'N/A')}")
        print(f"🎯 Classification risque : {resultats.get('classification_risque', 'N/A')}")
        print(f"🚨 Nb anomalies détectées : {resultats.get('nb_anomalies_total', 'N/A')}")
        
        if 'anomalies_detectees' in resultats:
            anomalies = resultats['anomalies_detectees']
            print(f"📋 Détail anomalies : {len(anomalies)} trouvées")
            
            # Afficher les 3 premières anomalies
            for i, anomalie in enumerate(anomalies[:3]):
                print(f"   {i+1}. {anomalie.get('type', 'N/A')} - {anomalie.get('severite', 'N/A')}")
        
        print("✅ Module INDEX5 Anomaly Detection : FONCTIONNEL")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test anomaly detection : {e}")
        traceback.print_exc()
        return False

def tester_module_adaptive_prediction():
    """Test du module de prédiction adaptative INDEX5"""
    print("\n" + "="*60)
    print("🔮 TEST MODULE : INDEX5 ADAPTIVE PREDICTION")
    print("="*60)
    
    try:
        from lupasco_refactored.statistics.index5_adaptive_prediction import Index5AdaptivePredictor
        
        # Charger données
        data = charger_donnees_json()
        if not data:
            return False
        
        sequence_index5 = extraire_sequence_index5(data)
        if not sequence_index5:
            return False
        
        # Tester le module
        predictor = Index5AdaptivePredictor()
        print(f"🎯 Test prédiction adaptative avec {len(sequence_index5)} éléments...")
        
        # Prendre un échantillon
        echantillon = sequence_index5[:500] if len(sequence_index5) > 500 else sequence_index5
        
        resultats = predictor.predire_index5_adaptatif(echantillon, nb_predictions=5)
        
        if 'erreur' in resultats:
            print(f"❌ Erreur dans la prédiction : {resultats['erreur']}")
            return False
        
        # Vérifier les résultats
        predictions = resultats.get('predictions', [])
        confiances = resultats.get('confiance_predictions', [])
        
        print(f"🔮 Nb prédictions générées : {len(predictions)}")
        print(f"📊 Confiance moyenne : {sum(confiances)/len(confiances):.3f}" if confiances else "N/A")
        
        # Afficher les prédictions
        print("📋 Prédictions générées :")
        for i, (pred, conf) in enumerate(zip(predictions[:5], confiances[:5])):
            print(f"   {i+1}. {pred} (confiance: {conf:.3f})")
        
        if 'performance_adaptative' in resultats:
            perf = resultats['performance_adaptative']
            print(f"⚙️  Ordre Markov optimal : {perf.get('ordre_markov_optimal', 'N/A')}")
            print(f"🧠 Patterns appris : {perf.get('nb_patterns_appris', 'N/A')}")
        
        print("✅ Module INDEX5 Adaptive Prediction : FONCTIONNEL")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test adaptive prediction : {e}")
        traceback.print_exc()
        return False

def tester_module_master_analyzer():
    """Test du module analyseur maître INDEX5"""
    print("\n" + "="*60)
    print("🎯 TEST MODULE : INDEX5 MASTER ANALYZER")
    print("="*60)
    
    try:
        from lupasco_refactored.analyzers.index5_master_analyzer import Index5MasterAnalyzer
        
        # Charger données
        data = charger_donnees_json()
        if not data:
            return False
        
        sequence_index5 = extraire_sequence_index5(data)
        if not sequence_index5:
            return False
        
        # Tester le module maître
        master = Index5MasterAnalyzer()
        print(f"🎯 Test analyseur maître avec {len(sequence_index5)} éléments...")
        
        # Prendre un échantillon pour test rapide
        echantillon = sequence_index5[:800] if len(sequence_index5) > 800 else sequence_index5
        
        resultats = master.analyser_index5_complet(
            echantillon, 
            nb_predictions=3,  # Réduire pour test rapide
            generer_rapport=True
        )
        
        if 'erreur' in resultats:
            print(f"❌ Erreur dans l'analyse maître : {resultats['erreur']}")
            return False
        
        # Vérifier les sections principales
        sections_attendues = ['analyse_entropique_avancee', 'detection_anomalies', 
                             'prediction_adaptative', 'synthese_globale']
        
        for section in sections_attendues:
            if section in resultats:
                print(f"✅ Section {section} : OK")
            else:
                print(f"❌ Section {section} : MANQUANTE")
        
        # Afficher synthèse globale
        if 'synthese_globale' in resultats:
            synthese = resultats['synthese_globale']
            print(f"📊 Classification INDEX5 : {synthese.get('classification_index5', 'N/A')}")
            print(f"📈 Score global : {synthese.get('scores_globaux', {}).get('global', 'N/A'):.1f}")
            print(f"⚠️  Niveau risque : {synthese.get('niveau_risque', 'N/A')}")
            
            # Recommandations
            reco = synthese.get('recommandations_prioritaires', [])
            print(f"📋 Nb recommandations : {len(reco)}")
        
        # Test sauvegarde
        nom_fichier = master.sauvegarder_resultats(resultats, "test_resultats_index5.json")
        if nom_fichier:
            print(f"💾 Sauvegarde JSON : {nom_fichier}")
        
        # Test rapport texte
        nom_rapport = master.generer_rapport_texte(resultats, "test_rapport_index5.txt")
        if nom_rapport:
            print(f"📄 Rapport texte : {nom_rapport}")
        
        print("✅ Module INDEX5 Master Analyzer : FONCTIONNEL")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test master analyzer : {e}")
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("🎓 TESTS COMPLETS MODULES INDEX5 AVANCÉS")
    print("="*80)
    print("Expert Statisticien IA - Vérification fonctionnement")
    print("="*80)
    
    # Compteur de succès
    tests_reussis = 0
    total_tests = 4
    
    # Test 1 : Module entropie avancée
    if tester_module_entropy_avancee():
        tests_reussis += 1
    
    # Test 2 : Module détection anomalies
    if tester_module_anomaly_detection():
        tests_reussis += 1
    
    # Test 3 : Module prédiction adaptative
    if tester_module_adaptive_prediction():
        tests_reussis += 1
    
    # Test 4 : Module analyseur maître
    if tester_module_master_analyzer():
        tests_reussis += 1
    
    # Résumé final
    print("\n" + "="*80)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*80)
    print(f"✅ Tests réussis : {tests_reussis}/{total_tests}")
    print(f"📈 Taux de succès : {(tests_reussis/total_tests)*100:.1f}%")
    
    if tests_reussis == total_tests:
        print("🎉 TOUS LES MODULES INDEX5 FONCTIONNENT PARFAITEMENT !")
        print("✅ Prêt pour intégration en production")
    else:
        print("⚠️  Certains modules nécessitent des corrections")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    print("="*80)
    
    return tests_reussis == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
