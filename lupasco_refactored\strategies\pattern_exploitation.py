"""
EXPLOITATION DES PATTERNS INDEX5 - STRATÉGIES OPTIMALES
=======================================================

Module spécialisé pour identifier et exploiter les patterns
à haut potentiel de succès détectés par l'analyse INDEX5.

Basé sur les résultats d'analyse de 6,6M+ éléments.

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

import numpy as np
from collections import defaultdict, Counter, deque
from typing import List, Dict, Tuple, Optional
import json
from datetime import datetime

class PatternExploiter:
    """
    Classe pour identifier et exploiter les patterns INDEX5
    à haut potentiel de succès (SANS TIE)
    """

    def __init__(self):
        """Initialise l'exploiteur de patterns"""
        self.patterns_markov = {}
        self.patterns_cycles = {}
        self.patterns_dependances = {}
        self.seuil_confiance = 0.45  # 45% minimum (plus réaliste)
        self.seuil_cycles = 0.35  # 35% pour cycles
        self.seuil_dependances = 0.40  # 40% pour dépendances
        self.min_occurrences_markov = 20  # Minimum 20 occurrences
        self.min_occurrences_deps = 30  # Minimum 30 pour dépendances
        self.historique_recent = deque(maxlen=10)  # 10 derniers coups

        # Définir les occurrences TIE à exclure (centralisé)
        self.occurrences_tie = {
            'SYNC_pair_4_TIE',
            'SYNC_pair_6_TIE',
            'SYNC_impair_5_TIE',
            'DESYNC_pair_4_TIE',
            'DESYNC_pair_6_TIE',
            'DESYNC_impair_5_TIE'
        }
        
    def analyser_patterns_exploitables(self, sequence_index5: List[str], 
                                     resultats_analyse: Dict) -> Dict:
        """
        Analyse les patterns exploitables depuis les résultats d'analyse
        
        Args:
            sequence_index5: Séquence INDEX5 complète
            resultats_analyse: Résultats de l'analyse maître
            
        Returns:
            dict: Patterns exploitables identifiés
        """
        print("🎯 IDENTIFICATION DES PATTERNS EXPLOITABLES (SANS TIE)")
        print("=" * 60)

        # Afficher statistiques d'exclusion TIE
        self._afficher_statistiques_exclusion_tie(sequence_index5)

        patterns_exploitables = {
            'patterns_markov_haute_confiance': [],
            'patterns_cycles_predictibles': [],
            'patterns_dependances_cachees': [],
            'strategies_recommandees': [],
            'zones_opportunite': []
        }
        
        # 1. Patterns Markoviens haute confiance
        patterns_markov = self._extraire_patterns_markov_exploitables(
            sequence_index5, resultats_analyse
        )
        patterns_exploitables['patterns_markov_haute_confiance'] = patterns_markov
        
        # 2. Patterns cycliques prédictibles
        patterns_cycles = self._extraire_patterns_cycles_exploitables(
            sequence_index5, resultats_analyse
        )
        patterns_exploitables['patterns_cycles_predictibles'] = patterns_cycles
        
        # 3. Dépendances cachées exploitables
        patterns_deps = self._extraire_dependances_exploitables(
            sequence_index5, resultats_analyse
        )
        patterns_exploitables['patterns_dependances_cachees'] = patterns_deps
        
        # 4. Générer stratégies d'exploitation
        strategies = self._generer_strategies_exploitation(patterns_exploitables)
        patterns_exploitables['strategies_recommandees'] = strategies
        
        # 5. Identifier zones d'opportunité
        zones = self._identifier_zones_opportunite(patterns_exploitables)
        patterns_exploitables['zones_opportunite'] = zones
        
        return patterns_exploitables
    
    def _extraire_patterns_markov_exploitables(self, sequence: List[str],
                                             resultats: Dict) -> List[Dict]:
        """Extrait les patterns Markoviens avec confiance > 60% (SANS TIE)"""
        patterns_haute_confiance = []

        # Analyser ordre 4 (optimal détecté)
        ordre = 4
        transitions = defaultdict(Counter)

        # Construire matrice de transitions ordre 4 (SANS TIE)
        for i in range(len(sequence) - ordre):
            contexte = tuple(sequence[i:i+ordre])
            suivant = sequence[i+ordre]

            # VÉRIFICATION CRITIQUE : Exclure si contexte ou suivant contient TIE
            contexte_contient_tie = any(element in self.occurrences_tie for element in contexte)
            suivant_est_tie = suivant in self.occurrences_tie

            if not contexte_contient_tie and not suivant_est_tie:
                transitions[contexte][suivant] += 1
        
        # Identifier patterns haute confiance
        for contexte, compteurs in transitions.items():
            total = sum(compteurs.values())
            if total >= self.min_occurrences_markov:  # Seuil ajustable
                for suivant, count in compteurs.items():
                    probabilite = count / total
                    if probabilite >= self.seuil_confiance:
                        pattern = {
                            'contexte': list(contexte),
                            'prediction': suivant,
                            'probabilite': probabilite,
                            'occurrences': count,
                            'total_contexte': total,
                            'confiance': probabilite,
                            'type': 'MARKOV_ORDRE_4'
                        }
                        patterns_haute_confiance.append(pattern)
        
        # Trier par confiance décroissante
        patterns_haute_confiance.sort(key=lambda x: x['confiance'], reverse=True)
        
        print(f"   🔗 {len(patterns_haute_confiance)} patterns Markov haute confiance")
        return patterns_haute_confiance[:20]  # Top 20
    
    def _extraire_patterns_cycles_exploitables(self, sequence: List[str], 
                                              resultats: Dict) -> List[Dict]:
        """Extrait les patterns cycliques exploitables"""
        patterns_cycles = []
        
        # Analyser cycles de différentes longueurs
        for longueur_cycle in range(8, 25):  # Cycles de 8 à 24 coups
            cycles_detectes = self._detecter_cycles_longueur(sequence, longueur_cycle)
            
            for cycle in cycles_detectes:
                if cycle['confiance'] >= self.seuil_cycles:  # Seuil ajustable
                    patterns_cycles.append(cycle)
        
        # Trier par confiance
        patterns_cycles.sort(key=lambda x: x['confiance'], reverse=True)
        
        print(f"   📈 {len(patterns_cycles)} patterns cycliques exploitables")
        return patterns_cycles[:15]  # Top 15
    
    def _detecter_cycles_longueur(self, sequence: List[str], longueur: int) -> List[Dict]:
        """Détecte les cycles d'une longueur donnée (SANS TIE)"""
        cycles = []

        # Extraire tous les segments de cette longueur (SANS TIE)
        segments = []
        for i in range(len(sequence) - longueur + 1):
            segment = tuple(sequence[i:i+longueur])

            # Vérifier qu'aucun élément du segment n'est un TIE
            segment_contient_tie = any(element in self.occurrences_tie for element in segment)

            if not segment_contient_tie:
                segments.append(segment)
        
        # Compter les répétitions
        compteur_segments = Counter(segments)
        
        # Identifier cycles fréquents
        for segment, count in compteur_segments.items():
            if count >= 10:  # Minimum 10 répétitions
                probabilite = count / len(segments)
                if probabilite >= 0.01:  # Au moins 1%
                    cycle = {
                        'pattern': list(segment),
                        'longueur': longueur,
                        'repetitions': count,
                        'probabilite': probabilite,
                        'confiance': min(probabilite * 10, 1.0),  # Ajustement confiance
                        'type': 'CYCLE_REPETITIF'
                    }
                    cycles.append(cycle)
        
        return cycles
    
    def _extraire_dependances_exploitables(self, sequence: List[str], 
                                         resultats: Dict) -> List[Dict]:
        """Extrait les dépendances cachées exploitables"""
        dependances = []
        
        # Analyser dépendances à distance (gaps)
        for gap in range(2, 8):  # Dépendances avec 2-7 coups d'écart
            deps_gap = self._analyser_dependances_gap(sequence, gap)
            dependances.extend(deps_gap)
        
        # Filtrer par confiance
        dependances_exploitables = [d for d in dependances if d['confiance'] >= self.seuil_dependances]
        dependances_exploitables.sort(key=lambda x: x['confiance'], reverse=True)
        
        print(f"   🔍 {len(dependances_exploitables)} dépendances cachées exploitables")
        return dependances_exploitables[:10]  # Top 10

    def _afficher_statistiques_exclusion_tie(self, sequence: List[str]):
        """Affiche les statistiques d'exclusion des TIE dans l'analyse des patterns"""
        print(f"\n🚫 EXCLUSIONS TIE DANS L'ANALYSE DES PATTERNS :")
        print("-" * 50)

        # Compter les TIE dans la séquence originale
        nb_tie_total = sum(1 for element in sequence if element in self.occurrences_tie)
        pourcentage_tie = (nb_tie_total / len(sequence)) * 100 if sequence else 0

        print(f"   📊 TIE dans séquence : {nb_tie_total:,} ({pourcentage_tie:.1f}%)")
        print(f"   🚫 Exclusions appliquées :")
        for tie in sorted(self.occurrences_tie):
            count = sequence.count(tie)
            if count > 0:
                print(f"      ❌ {tie:<20} : {count:4,}")

        print(f"   ✅ Patterns analysés : UNIQUEMENT PLAYER/BANKER")
        print(f"   🎯 Avantage : Patterns plus prédictibles et exploitables")
    
    def _analyser_dependances_gap(self, sequence: List[str], gap: int) -> List[Dict]:
        """Analyse les dépendances avec un gap donné (SANS TIE)"""
        dependances = []
        transitions_gap = defaultdict(Counter)

        # Analyser transitions avec gap (SANS TIE)
        for i in range(len(sequence) - gap - 1):
            element_source = sequence[i]
            element_cible = sequence[i + gap + 1]

            # Vérifier qu'aucun élément n'est un TIE
            if element_source not in self.occurrences_tie and element_cible not in self.occurrences_tie:
                transitions_gap[element_source][element_cible] += 1
        
        # Identifier dépendances significatives
        for source, compteurs in transitions_gap.items():
            total = sum(compteurs.values())
            if total >= self.min_occurrences_deps:  # Seuil ajustable
                for cible, count in compteurs.items():
                    probabilite = count / total
                    # Comparer à la probabilité de base
                    prob_base = sequence.count(cible) / len(sequence)
                    ratio_enrichissement = probabilite / prob_base if prob_base > 0 else 0

                    if ratio_enrichissement >= 1.3 and probabilite >= 0.12:  # Seuils plus bas
                        dependance = {
                            'source': source,
                            'cible': cible,
                            'gap': gap,
                            'probabilite': probabilite,
                            'probabilite_base': prob_base,
                            'enrichissement': ratio_enrichissement,
                            'confiance': min(probabilite * ratio_enrichissement / 2, 1.0),
                            'occurrences': count,
                            'type': f'DEPENDANCE_GAP_{gap}'
                        }
                        dependances.append(dependance)
        
        return dependances
    
    def _generer_strategies_exploitation(self, patterns: Dict) -> List[Dict]:
        """Génère des stratégies d'exploitation basées sur les patterns"""
        strategies = []
        
        # Stratégie 1: Exploitation Markov haute confiance
        patterns_markov = patterns['patterns_markov_haute_confiance']
        if patterns_markov:
            top_markov = patterns_markov[0]
            strategie_markov = {
                'nom': 'EXPLOITATION_MARKOV_HAUTE_CONFIANCE',
                'description': f'Exploiter pattern Markov avec {top_markov["confiance"]:.1%} de confiance',
                'pattern_cle': top_markov,
                'conditions_activation': {
                    'contexte_requis': top_markov['contexte'],
                    'confiance_minimum': 0.60
                },
                'action_recommandee': f'Miser sur {top_markov["prediction"]}',
                'esperance_gain': top_markov['confiance'] - 0.5,  # Gain attendu vs hasard
                'risque': 1 - top_markov['confiance'],
                'priorite': 1
            }
            strategies.append(strategie_markov)
        
        # Stratégie 2: Exploitation cycles
        patterns_cycles = patterns['patterns_cycles_predictibles']
        if patterns_cycles:
            top_cycle = patterns_cycles[0]
            strategie_cycle = {
                'nom': 'EXPLOITATION_CYCLES_PREDICTIBLES',
                'description': f'Exploiter cycle de {top_cycle["longueur"]} coups',
                'pattern_cle': top_cycle,
                'conditions_activation': {
                    'position_dans_cycle': 'detecter_automatiquement',
                    'confiance_minimum': 0.55
                },
                'action_recommandee': 'Suivre le pattern cyclique',
                'esperance_gain': top_cycle['confiance'] - 0.5,
                'risque': 1 - top_cycle['confiance'],
                'priorite': 2
            }
            strategies.append(strategie_cycle)
        
        # Stratégie 3: Exploitation dépendances
        patterns_deps = patterns['patterns_dependances_cachees']
        if patterns_deps:
            top_dep = patterns_deps[0]
            strategie_dep = {
                'nom': 'EXPLOITATION_DEPENDANCES_CACHEES',
                'description': f'Exploiter dépendance {top_dep["source"]} → {top_dep["cible"]}',
                'pattern_cle': top_dep,
                'conditions_activation': {
                    'element_declencheur': top_dep['source'],
                    'gap_attente': top_dep['gap']
                },
                'action_recommandee': f'Après {top_dep["source"]}, attendre {top_dep["gap"]} coups puis miser sur {top_dep["cible"]}',
                'esperance_gain': (top_dep['confiance'] - 0.5) * top_dep['enrichissement'],
                'risque': 1 - top_dep['confiance'],
                'priorite': 3
            }
            strategies.append(strategie_dep)
        
        return strategies
    
    def _identifier_zones_opportunite(self, patterns: Dict) -> List[Dict]:
        """Identifie les zones d'opportunité maximale"""
        zones = []
        
        # Zone 1: Convergence de patterns
        if (patterns['patterns_markov_haute_confiance'] and 
            patterns['patterns_cycles_predictibles']):
            zone_convergence = {
                'nom': 'ZONE_CONVERGENCE_PATTERNS',
                'description': 'Zone où patterns Markov et cycliques convergent',
                'opportunite': 'TRÈS_ÉLEVÉE',
                'confiance_combinee': 0.75,  # Estimation conservative
                'action': 'Exploitation maximale recommandée',
                'conditions': 'Patterns Markov ET cycliques actifs simultanément'
            }
            zones.append(zone_convergence)
        
        # Zone 2: Dépendances multiples
        deps_fortes = [d for d in patterns['patterns_dependances_cachees'] 
                      if d['confiance'] >= 0.65]
        if len(deps_fortes) >= 2:
            zone_deps = {
                'nom': 'ZONE_DEPENDANCES_MULTIPLES',
                'description': 'Zone avec plusieurs dépendances cachées actives',
                'opportunite': 'ÉLEVÉE',
                'confiance_combinee': 0.68,
                'action': 'Exploitation séquentielle des dépendances',
                'conditions': 'Au moins 2 dépendances fortes simultanées'
            }
            zones.append(zone_deps)
        
        return zones
    
    def predire_prochaine_opportunite(self, historique_recent: List[str], 
                                    patterns_exploitables: Dict) -> Dict:
        """Prédit la prochaine opportunité d'exploitation"""
        self.historique_recent.extend(historique_recent[-10:])  # Garder 10 derniers
        
        opportunites = []
        
        # Vérifier patterns Markov
        for pattern in patterns_exploitables['patterns_markov_haute_confiance']:
            contexte = pattern['contexte']
            if len(self.historique_recent) >= len(contexte):
                historique_fin = list(self.historique_recent)[-len(contexte):]
                if historique_fin == contexte:
                    opportunite = {
                        'type': 'MARKOV_IMMEDIAT',
                        'prediction': pattern['prediction'],
                        'confiance': pattern['confiance'],
                        'action': f'Miser sur {pattern["prediction"]}',
                        'priorite': 1
                    }
                    opportunites.append(opportunite)
        
        # Vérifier dépendances avec gap
        for dep in patterns_exploitables['patterns_dependances_cachees']:
            gap = dep['gap']
            if len(self.historique_recent) >= gap + 1:
                element_source = list(self.historique_recent)[-(gap + 1)]
                if element_source == dep['source']:
                    opportunite = {
                        'type': 'DEPENDANCE_ACTIVEE',
                        'prediction': dep['cible'],
                        'confiance': dep['confiance'],
                        'action': f'Miser sur {dep["cible"]} (dépendance activée)',
                        'priorite': 2
                    }
                    opportunites.append(opportunite)
        
        # Retourner la meilleure opportunité
        if opportunites:
            opportunites.sort(key=lambda x: (x['priorite'], -x['confiance']))
            return opportunites[0]
        else:
            return {'type': 'AUCUNE_OPPORTUNITE', 'action': 'Attendre pattern favorable'}
    
    def sauvegarder_patterns_exploitables(self, patterns: Dict, nom_fichier: str):
        """Sauvegarde les patterns exploitables"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_complet = f"patterns_exploitables_{timestamp}_{nom_fichier}.json"
        
        with open(nom_complet, 'w', encoding='utf-8') as f:
            json.dump(patterns, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"💾 Patterns sauvegardés : {nom_complet}")
        return nom_complet

    def generer_rapport_exploitation(self, patterns: Dict, nom_fichier: str):
        """Génère un rapport d'exploitation lisible"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_rapport = f"rapport_exploitation_{timestamp}_{nom_fichier}.txt"

        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("RAPPORT D'EXPLOITATION DES PATTERNS INDEX5\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Date de génération : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Expert Statisticien IA - Patterns à haut potentiel\n\n")

            # Patterns Markov
            f.write("🔗 PATTERNS MARKOVIENS HAUTE CONFIANCE\n")
            f.write("-" * 50 + "\n")
            for i, pattern in enumerate(patterns['patterns_markov_haute_confiance'][:10], 1):
                f.write(f"{i:2d}. Contexte : {' → '.join(pattern['contexte'])}\n")
                f.write(f"    Prédiction : {pattern['prediction']}\n")
                f.write(f"    Confiance : {pattern['confiance']:.1%}\n")
                f.write(f"    Occurrences : {pattern['occurrences']}\n\n")

            # Stratégies
            f.write("🎯 STRATÉGIES D'EXPLOITATION RECOMMANDÉES\n")
            f.write("-" * 50 + "\n")
            for i, strategie in enumerate(patterns['strategies_recommandees'], 1):
                f.write(f"{i}. {strategie['nom']}\n")
                f.write(f"   Description : {strategie['description']}\n")
                f.write(f"   Action : {strategie['action_recommandee']}\n")
                f.write(f"   Espérance gain : {strategie['esperance_gain']:.1%}\n")
                f.write(f"   Risque : {strategie['risque']:.1%}\n\n")

            # Zones d'opportunité
            f.write("🚀 ZONES D'OPPORTUNITÉ MAXIMALE\n")
            f.write("-" * 50 + "\n")
            for i, zone in enumerate(patterns['zones_opportunite'], 1):
                f.write(f"{i}. {zone['nom']}\n")
                f.write(f"   Opportunité : {zone['opportunite']}\n")
                f.write(f"   Confiance : {zone['confiance_combinee']:.1%}\n")
                f.write(f"   Action : {zone['action']}\n\n")

        print(f"📋 Rapport généré : {nom_rapport}")
        return nom_rapport


# Fonctions d'export
__all__ = [
    'PatternExploiter'
]
