"""
CORE - DATA LOADER
==================

Module contenant la logique de chargement de données
pour l'analyseur Lupasco refactorisé.

Classe DataLoader pour gérer le chargement streaming et standard.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

import json
import gc
import os
from typing import Dict, List, Tuple, Optional


class DataLoader:
    """
    Classe pour gérer le chargement de données JSON
    Supporte le chargement streaming et standard
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise le DataLoader
        
        Args:
            fichier_json: Chemin vers le fichier JSON à charger
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.nb_parties_total = 0
        
    def charger_donnees(self, force_streaming: bool = False) -> Dict:
        """
        Charge les données avec la méthode appropriée
        
        Args:
            force_streaming: Force l'utilisation du streaming
            
        Returns:
            dict: Données chargées et séquences extraites
        """
        print(f"📂 Fichier à analyser : {os.path.basename(self.fichier_json)}")
        
        # Déterminer la méthode de chargement
        taille_fichier = os.path.getsize(self.fichier_json) / (1024 * 1024)  # MB
        print(f"📁 Taille du fichier : {taille_fichier:.1f} MB")
        
        if force_streaming or taille_fichier > 100:  # > 100 MB
            print("🔄 Chargement streaming...")
            return self._charger_avec_streaming()
        else:
            print("🔄 Chargement standard...")
            return self._charger_standard()
    
    def _charger_avec_streaming(self) -> Dict:
        """
        Charge le fichier JSON avec streaming ijson (pour fichiers volumineux)
        
        Returns:
            dict: Résultats du chargement streaming
        """
        try:
            import ijson
        except ImportError:
            print("⚠️ ijson non disponible, utilisation du chargement standard")
            return self._charger_standard()
        
        print("🔄 Lecture streaming du fichier JSON...")
        
        # Initialiser les listes de séquences directement
        index1_seq = []
        index2_seq = []
        index3_seq = []
        index5_seq = []  # INDEX5 : Combinaisons complètes
        index1_index2_seq = []
        index1_index3_seq = []
        index2_index3_seq = []
        index1_index2_index3_seq = []
        
        total_parties = 0
        total_mains = 0
        
        # Nettoyage mémoire préalable
        gc.collect()
        
        try:
            with open(self.fichier_json, 'rb') as f:
                print("   📖 Parsing streaming des parties...")
                
                # Parser streaming des parties
                parties = ijson.items(f, 'parties.item')
                
                for partie in parties:
                    total_parties += 1
                    
                    # Traiter chaque main de la partie
                    for main in partie['mains']:
                        idx1 = main['index1_sync_state']
                        idx2 = main['index2_cards_category']
                        idx3 = main['index3_result']
                        
                        # INDEX individuels
                        index1_seq.append(idx1)
                        index2_seq.append(idx2)
                        index3_seq.append(idx3)  # Garder tous les résultats y compris TIE
                        
                        # INDEX5 : Combinaison complète
                        idx5 = main['index5_combined']
                        index5_seq.append(idx5)
                        
                        # Combinaisons avec INDEX3 (BANKER, PLAYER, TIE)
                        index1_index3_seq.append(f"{idx1}_{idx3}")
                        index2_index3_seq.append(f"{idx2}_{idx3}")
                        index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")
                        
                        # Combinaisons sans INDEX3
                        index1_index2_seq.append(f"{idx1}_{idx2}")
                        
                        total_mains += 1
                    
                    # Ajouter un marqueur de fin de partie pour séparer les parties
                    # Ceci empêche les runs de traverser les frontières entre parties
                    # Note: On ne connaît pas le nombre total de parties en streaming,
                    # donc on ajoute toujours le marqueur et on le supprimera à la fin si nécessaire
                    
                    # Ajouter marqueurs sauf pour la dernière partie (on ne sait pas encore si c'est la dernière)
                    index1_seq.append("__FIN_PARTIE__")
                    index2_seq.append("__FIN_PARTIE__")
                    index3_seq.append("__FIN_PARTIE__")
                    index5_seq.append("__FIN_PARTIE__")
                    index1_index2_seq.append("__FIN_PARTIE__")
                    index1_index3_seq.append("__FIN_PARTIE__")
                    index2_index3_seq.append("__FIN_PARTIE__")
                    index1_index2_index3_seq.append("__FIN_PARTIE__")

                    # Affichage du progrès tous les 5000 parties
                    if total_parties % 5000 == 0:
                        print(f"   📈 Parties traitées : {total_parties:,} - {total_mains:,} mains extraites")
                        gc.collect()  # Nettoyage mémoire périodique

            # Supprimer le dernier marqueur de chaque séquence (après la dernière partie)
            if index1_seq and index1_seq[-1] == "__FIN_PARTIE__":
                index1_seq.pop()
                index2_seq.pop()
                index3_seq.pop()
                index5_seq.pop()
                index1_index2_seq.pop()
                index1_index3_seq.pop()
                index2_index3_seq.pop()
                index1_index2_index3_seq.pop()
            
            # Stocker les séquences
            self.sequences = {
                'INDEX1': index1_seq,
                'INDEX2': index2_seq,
                'INDEX3': index3_seq,
                'INDEX5': index5_seq,  # INDEX5 : Combinaisons complètes
                'INDEX1_INDEX2': index1_index2_seq,
                'INDEX1_INDEX3': index1_index3_seq,
                'INDEX2_INDEX3': index2_index3_seq,
                'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq
            }
            
            print(f"✅ Streaming terminé : {total_parties:,} parties, {total_mains:,} mains")
            print(f"   - INDEX1 : {len(index1_seq):,} valeurs")
            print(f"   - INDEX2 : {len(index2_seq):,} valeurs")
            print(f"   - INDEX3 : {len(index3_seq):,} valeurs")
            print(f"   - INDEX5 : {len(index5_seq):,} valeurs")
            print(f"   - Combinaisons : {len(index1_index2_seq):,} INDEX1_INDEX2")
            
            # Stocker le nombre de parties pour le rapport
            self.nb_parties_total = total_parties
            
            # Nettoyage final
            gc.collect()
            
            return {
                'sequences': self.sequences,
                'nb_parties_total': self.nb_parties_total,
                'total_mains': total_mains,
                'methode': 'streaming'
            }
            
        except Exception as e:
            print(f"❌ Erreur streaming : {e}")
            raise
    
    def _charger_standard(self) -> Dict:
        """
        Chargement standard pour fichiers plus petits
        
        Returns:
            dict: Résultats du chargement standard
        """
        gc.collect()
        
        with open(self.fichier_json, 'r', encoding='utf-8') as f:
            print("   📖 Parsing JSON standard...")
            self.donnees = json.load(f)
        
        nb_parties = len(self.donnees['parties'])
        print(f"✅ {nb_parties:,} parties chargées")
        
        # Stocker le nombre de parties pour le rapport
        self.nb_parties_total = nb_parties
        
        # Extraire les séquences
        total_mains = self._extraire_sequences()
        
        return {
            'sequences': self.sequences,
            'nb_parties_total': self.nb_parties_total,
            'total_mains': total_mains,
            'methode': 'standard',
            'donnees': self.donnees  # Conserver pour compatibilité
        }
    
    def _extraire_sequences(self) -> int:
        """
        Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties
        
        Returns:
            int: Nombre total de mains extraites
        """
        print("🔍 Extraction des séquences et combinaisons...")
        
        # Initialiser les listes de séquences individuelles
        index1_seq = []  # SYNC/DESYNC
        index2_seq = []  # pair_4/pair_6/impair_5
        index3_seq = []  # PLAYER/BANKER/TIE (garder TIE pour INDEX3 complet)
        index5_seq = []  # INDEX5 : Combinaison INDEX1_INDEX2_INDEX3 (18 combinaisons)
        
        # Initialiser les listes de séquences combinées
        index1_index2_seq = []  # SYNC/DESYNC + pair_4/pair_6/impair_5
        index1_index3_seq = []  # SYNC/DESYNC + PLAYER/BANKER
        index2_index3_seq = []  # pair_4/pair_6/impair_5 + PLAYER/BANKER
        index1_index2_index3_seq = []  # Combinaison des 3 INDEX

        # Créer INDEX9 et INDEX10 comme indices simples pour les calculs statistiques
        index9_seq = []  # INDEX9 = INDEX1_INDEX3 comme séquence simple
        index10_seq = []  # INDEX10 = INDEX2_INDEX3 comme séquence simple
        
        total_mains = 0
        nb_parties = len(self.donnees['parties'])
        
        # Définir l'intervalle d'affichage selon le nombre de parties
        if nb_parties <= 1000:
            intervalle = 100
        elif nb_parties <= 10000:
            intervalle = 1000
        else:
            intervalle = 10000  # Pour 100 000 parties, affichage tous les 10 000
        
        print(f"📊 Traitement de {nb_parties:,} parties (affichage tous les {intervalle:,})...")
        
        for i, partie in enumerate(self.donnees['parties'], 1):
            for main in partie['mains']:
                # INDEX individuels
                idx1 = main['index1_sync_state']
                idx2 = main['index2_cards_category']
                idx3 = main['index3_result']
                
                index1_seq.append(idx1)
                index2_seq.append(idx2)
                index3_seq.append(idx3)  # Garder tous les résultats y compris TIE
                
                # INDEX5 : Combinaison complète INDEX1_INDEX2_INDEX3
                idx5 = main['index5_combined']
                index5_seq.append(idx5)
                
                # Combinaisons avec INDEX3 (BANKER, PLAYER, TIE)
                index1_index3_seq.append(f"{idx1}_{idx3}")
                index2_index3_seq.append(f"{idx2}_{idx3}")
                index1_index2_index3_seq.append(f"{idx1}_{idx2}_{idx3}")

                # Créer INDEX9 et INDEX10 comme séquences simples
                index9_seq.append(f"{idx1}_{idx3}")  # INDEX9 = INDEX1_INDEX3 comme séquence simple
                index10_seq.append(f"{idx2}_{idx3}")  # INDEX10 = INDEX2_INDEX3 comme séquence simple

                # Combinaisons sans INDEX3 (toujours possibles)
                index1_index2_seq.append(f"{idx1}_{idx2}")
                
                total_mains += 1
            
            # Ajouter un marqueur de fin de partie pour séparer les parties
            # Ceci empêche les runs de traverser les frontières entre parties
            if i < nb_parties:  # Éviter d'ajouter après la dernière partie
                index1_seq.append("__FIN_PARTIE__")
                index2_seq.append("__FIN_PARTIE__")
                index3_seq.append("__FIN_PARTIE__")
                index5_seq.append("__FIN_PARTIE__")
                index1_index2_seq.append("__FIN_PARTIE__")
                index1_index3_seq.append("__FIN_PARTIE__")
                index2_index3_seq.append("__FIN_PARTIE__")
                index1_index2_index3_seq.append("__FIN_PARTIE__")
                index9_seq.append("__FIN_PARTIE__")  # INDEX9
                index10_seq.append("__FIN_PARTIE__")  # INDEX10
            
            # Affichage cyclique du progrès
            if i % intervalle == 0 or i == nb_parties:
                pourcentage = (i / nb_parties) * 100
                print(f"   📈 Parties traitées : {i:,}/{nb_parties:,} ({pourcentage:.1f}%) - {total_mains:,} mains extraites")
        
        # Stocker toutes les séquences
        self.sequences = {
            # INDEX individuels
            'INDEX1': index1_seq,
            'INDEX2': index2_seq,
            'INDEX3': index3_seq,
            'INDEX5': index5_seq,  # INDEX5 : Combinaison complète

            # Combinaisons d'INDEX (pour compatibilité)
            'INDEX1_INDEX2': index1_index2_seq,
            'INDEX1_INDEX3': index1_index3_seq,
            'INDEX2_INDEX3': index2_index3_seq,
            'INDEX1_INDEX2_INDEX3': index1_index2_index3_seq,

            # INDEX9 et INDEX10 comme séquences simples pour calculs statistiques
            'INDEX9': index9_seq,   # INDEX9 = INDEX1_INDEX3 comme séquence simple
            'INDEX10': index10_seq  # INDEX10 = INDEX2_INDEX3 comme séquence simple
        }
        
        print(f"✅ {total_mains:,} mains extraites")
        print(f"   - INDEX1 (SYNC/DESYNC) : {len(index1_seq):,} valeurs")
        print(f"   - INDEX2 (pair_4/6/impair_5) : {len(index2_seq):,} valeurs")
        print(f"   - INDEX3 (PLAYER/BANKER/TIE) : {len(index3_seq):,} valeurs")
        print(f"   - INDEX5 (Combinaisons complètes) : {len(index5_seq):,} valeurs")
        print(f"   - INDEX1_INDEX2 : {len(index1_index2_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX3 : {len(index1_index3_seq):,} combinaisons")
        print(f"   - INDEX2_INDEX3 : {len(index2_index3_seq):,} combinaisons")
        print(f"   - INDEX1_INDEX2_INDEX3 : {len(index1_index2_index3_seq):,} combinaisons")
        print(f"   - INDEX9 (INDEX1_INDEX3 simple) : {len(index9_seq):,} valeurs")
        print(f"   - INDEX10 (INDEX2_INDEX3 simple) : {len(index10_seq):,} valeurs")
        
        return total_mains


# Fonctions d'export pour compatibilité
__all__ = [
    'DataLoader'
]
