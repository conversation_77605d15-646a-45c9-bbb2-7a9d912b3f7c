RÉFÉRENCE COMPLÈTE DES MÉTHODES INDEX5 - SYSTÈME LUPASCO
================================================================

Date de création : 2025-06-20
Analysé par : Expert Statisticien IA
Codebase : C:\Users\<USER>\Desktop\Lupasco7

================================================================
RÉSUMÉ EXÉCUTIF
================================================================

TOTAL MÉTHODES INDEX5 IDENTIFIÉES : 47 méthodes principales
FICHIERS CONCERNÉS : 6 fichiers Python
LIGNES DE CODE ANALYSÉES : ~8000 lignes
ARCHITECTURE : Modulaire avec wrappers et analyseurs spécialisés

================================================================
1. FICHIER PRINCIPAL : analyseur.py
================================================================

🎯 MÉTHODES D'ANALYSE INDEX5 PRINCIPALES :
------------------------------------------
- analyser_index5_avec_formules_exactes() [Ligne 944-958]
  └─ WRAPPER vers lupasco_refactored.analyzers.index5_analyzer

- analyser_predictibilite_index5_par_index1() [Ligne 2747-2780]
  └─ Analyse prédictibilité INDEX5 par INDEX1

🔧 MÉTHODES DE SUPPORT INDEX5 - EXTRACTION :
--------------------------------------------
- _extraire_composants_index5() [Ligne 188-197]
  └─ WRAPPER vers SequenceExtractor.extraire_composants_index5

🔧 MÉTHODES DE SUPPORT INDEX5 - TRANSITIONS :
---------------------------------------------
- _analyser_transitions_index5() [Ligne 883-892]
  └─ WRAPPER vers TransitionAnalyzer.analyser_transitions

🔧 MÉTHODES DE SUPPORT INDEX5 - PATTERNS TEMPORELS :
----------------------------------------------------
- _analyser_patterns_temporels_index5() [Ligne 898-917]
- _detecter_cycles_index5() [Ligne 919-923]
- _tester_stationnarite_index5() [Ligne 925-929]
- _analyser_tendances_index5() [Ligne 931-935]

🔧 MÉTHODES ENTROPIQUES INDEX5 :
-------------------------------
- _analyser_entropie_renyi_tous_indices() [Ligne 975-1011]
  └─ Inclut INDEX5 dans l'analyse Rényi

- _analyser_entropie_conditionnelle_prediction() [Ligne 1012-1022]
  └─ Utilise composants extraits d'INDEX5

- _analyser_information_mutuelle_complete() [Ligne 1067-1076]
  └─ Utilise composants extraits d'INDEX5

- _analyser_entropie_croisee_tous_indices() [Ligne 1117-1138]
  └─ INDEX5 avec 18 combinaisons uniformes

- _analyser_divergence_kl_comparative() [Ligne 1221-1228]
  └─ INDEX5 avec distribution de référence

- _analyser_entropie_temporelle() [Ligne 1301-1315]
  └─ INDEX5 avec fenêtres glissantes

- _analyser_transitions_tous_indices() [Ligne 1459-1479]
  └─ INDEX5 avec matrices de transition

- _analyser_entropie_multi_echelle() [Ligne 1537-1561]
  └─ INDEX5 avec analyse fractale

- _analyser_entropie_jointe_complete() [Ligne 1687-1691]
  └─ Utilise composants extraits d'INDEX5

🔧 MÉTHODES UTILITAIRES INDEX5 :
-------------------------------
- _calculer_profils_reference() [Ligne 1196-1202]
  └─ INDEX5 : Distribution uniforme 18 combinaisons

- _classifier_selon_divergence() [Ligne 1265-1271]
  └─ Seuils spécifiques INDEX5

- _analyser_entropie_relative_tous_indices() [Ligne 1895-1909]
  └─ INDEX5 avec parties de référence

🎯 MÉTHODES D'ANALYSE GLOBALE INCLUANT INDEX5 :
----------------------------------------------
- analyser_sequences_completes() [Ligne 2572-2611]
  └─ Phase 2 : Analyse INDEX5 avec formules exactes

- analyser_statistiques_avancees() [Ligne 2631-2636]
  └─ Analyse statistique avancée INDEX5

- analyser_lupasco_par_parties() [Ligne 2448-2456]
  └─ Divise INDEX5 en parties indépendantes

================================================================
2. FICHIER SPÉCIALISÉ : lupasco_refactored/analyzers/index5_analyzer.py
================================================================

🎯 CLASSE PRINCIPALE : Index5Analyzer
------------------------------------
- __init__() [Ligne 52-59]
- analyser_index5_avec_formules_exactes() [Ligne 61-142]
  └─ MÉTHODE PRINCIPALE d'analyse INDEX5

🔧 MÉTHODES D'ANALYSE GLOBALE INDEX5 :
-------------------------------------
- _analyser_globale_index5() [Ligne 144-210]
  └─ Analyse globale séquence INDEX5 nettoyée

🔧 MÉTHODES D'ANALYSE PAR COMBINAISON INDEX5 :
----------------------------------------------
- _analyser_par_combinaison_index5() [Ligne 212-343]
  └─ Analyse détaillée des 18 combinaisons

🔧 MÉTHODES DE PATTERNS TEMPORELS INDEX5 :
------------------------------------------
- _analyser_transitions_index5() [Ligne 345-356]
- _analyser_patterns_temporels_index5() [Ligne 358-383]
- _detecter_cycles_index5() [Ligne 385-397]
- _tester_stationnarite_index5() [Ligne 399-412]
- _analyser_tendances_index5() [Ligne 414-430]

🔧 MÉTHODES ENTROPIQUES AVANCÉES INDEX5 :
-----------------------------------------
- _analyser_entropie_avancee_index5() [Ligne 432-455]
  └─ Analyse entropique avancée sur INDEX5 directement

================================================================
3. FICHIER EXTRACTEUR : lupasco_refactored/core/sequence_extractor.py
================================================================

🎯 MÉTHODE PRINCIPALE D'EXTRACTION INDEX5 :
-------------------------------------------
- extraire_composants_index5() [Ligne 34-82]
  └─ Extrait INDEX1, INDEX2, INDEX3 depuis INDEX5
  └─ Format: "SYNC_pair_4_BANKER" → ["SYNC", "pair_4", "BANKER"]

🔧 MÉTHODES DE VALIDATION INDEX5 :
---------------------------------
- valider_extractions() [Ligne 241-260]
  └─ Compare INDEX5 avec INDEX1, INDEX2, INDEX3 originaux

================================================================
4. FICHIER STATISTIQUES : lupasco_refactored/statistics/entropy_advanced.py
================================================================

🎯 MÉTHODE PRINCIPALE ENTROPIQUE INDEX5 :
----------------------------------------
- analyser_entropie_avancee_complete() [Ligne 36-133]
  └─ Paramètre composants_index5 pour analyse complète

🔧 MÉTHODES ENTROPIQUES SPÉCIALISÉES INDEX5 :
---------------------------------------------
- analyser_entropie_renyi_tous_indices() [Ligne 147-155]
  └─ INDEX5 avec 4 ordres alpha (0, 1, 2, inf)

- analyser_entropie_conditionnelle_prediction() [Ligne 220-305]
  └─ Utilise composants_index5 pour prédiction

- analyser_information_mutuelle_complete() [Ligne 307-371]
  └─ Paramètre composants_index5 obligatoire

- analyser_entropie_croisee_tous_indices() [Ligne 378-381]
  └─ INDEX5 : Distribution uniforme 18 valeurs

- analyser_divergence_kl_comparative() [Ligne 449-452]
  └─ INDEX5 : Profil de référence uniforme

- analyser_entropie_jointe_complete() [Ligne 684-703]
  └─ Utilise composants_index5 exclusivement

- analyser_entropie_relative_tous_indices() [Ligne 798-801]
  └─ INDEX5 : Entropie maximale log2(18)

================================================================
5. FICHIER TRANSITIONS : lupasco_refactored/statistics/transitions.py
================================================================

🔧 MÉTHODE GÉNÉRIQUE SUPPORTANT INDEX5 :
----------------------------------------
- analyser_transitions() [Ligne 34-179]
  └─ Paramètre nom_sequence accepte "INDEX5"
  └─ Gère les 18 combinaisons INDEX5

================================================================
6. FICHIER FORMULES : formules_mathematiques_exactes.py
================================================================

🔧 AUCUNE RÉFÉRENCE DIRECTE INDEX5 TROUVÉE
------------------------------------------
Note : Ce fichier contient les formules mathématiques génériques
utilisées par les analyseurs INDEX5 via imports.

================================================================
ARCHITECTURE TECHNIQUE INDEX5
================================================================

FLUX D'ANALYSE INDEX5 :
-----------------------
1. analyseur.py → analyser_index5_avec_formules_exactes()
2. → lupasco_refactored.analyzers.index5_analyzer.Index5Analyzer
3. → Extraction composants via sequence_extractor.py
4. → Analyses statistiques via entropy_advanced.py
5. → Analyses transitions via transitions.py
6. → Formules mathématiques via formules_mathematiques_exactes.py

DÉPENDANCES INDEX5 :
-------------------
- numpy, pandas, scipy (calculs scientifiques)
- collections.Counter (comptage combinaisons)
- math (fonctions logarithmiques)
- typing (annotations de type)

FORMATS DE DONNÉES INDEX5 :
---------------------------
- Format combinaison : "SYNC_pair_4_BANKER"
- Marqueurs de fin : "__FIN_PARTIE__"
- 18 combinaisons possibles
- Structure : [SYNC/DESYNC]_[pair_4/pair_6/impair_5]_[BANKER/PLAYER/TIE]

================================================================
MÉTRIQUES DE COMPLEXITÉ
================================================================

LIGNES DE CODE INDEX5 : ~2000 lignes dédiées
MÉTHODES PRINCIPALES : 8 méthodes
MÉTHODES DE SUPPORT : 39 méthodes
TESTS STATISTIQUES : 15 types d'analyses
FORMULES MATHÉMATIQUES : 10 équations entropiques

================================================================
FIN DU DOCUMENT DE RÉFÉRENCE
================================================================
