"""
UTILS - DATA UTILITIES
======================

Module contenant les utilitaires de manipulation de données
pour l'analyseur Lupasco refactorisé.

Fonctions utilitaires de base sans dépendances complexes.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

from collections import Counter
from typing import List, Union
import numpy as np


def nettoyer_marqueurs(sequence: List[str]) -> List[str]:
    """
    Supprime les marqueurs __FIN_PARTIE__ d'une séquence
    
    Args:
        sequence: Séquence avec marqueurs potentiels
        
    Returns:
        list: Séquence nettoyée sans marqueurs
    """
    return [x for x in sequence if x != "__FIN_PARTIE__"]


def convertir_sequence_numerique(sequence: List[str]) -> List[int]:
    """
    Convertit une séquence de chaînes en valeurs numériques pour l'autocorrélation
    
    Args:
        sequence: Séquence de chaînes
        
    Returns:
        list: Séquence numérique
    """
    # Créer un mapping des valeurs uniques vers des entiers
    valeurs_uniques = list(set(sequence))
    mapping = {val: i for i, val in enumerate(valeurs_uniques)}
    
    return [mapping[val] for val in sequence]


def calculer_distribution(data: List[str], nb_valeurs_possibles: int) -> List[float]:
    """
    Calcule la distribution de probabilité d'une séquence
    
    Args:
        data: Séquence de données
        nb_valeurs_possibles: Nombre total de valeurs possibles
        
    Returns:
        list: Distribution de probabilité
    """
    # Compter les occurrences
    compteur = Counter(data)
    
    # Créer la distribution
    distribution = [0.0] * nb_valeurs_possibles
    
    # Mapper les valeurs aux indices
    valeurs_uniques = sorted(set(data))
    
    for i, valeur in enumerate(valeurs_uniques):
        if i < nb_valeurs_possibles:
            distribution[i] = compteur[valeur] / len(data)
    
    return distribution


def calculer_entropie_shannon(sequence: List[str]) -> float:
    """
    Calcule l'entropie de Shannon de la séquence
    
    Args:
        sequence: Séquence à analyser
        
    Returns:
        float: Entropie de Shannon en bits
    """
    # Compter les fréquences
    compteur = Counter(sequence)
    n = len(sequence)
    
    # Calculer l'entropie
    entropie = 0.0
    for count in compteur.values():
        p = count / n
        if p > 0:
            entropie -= p * np.log2(p)
    
    return entropie


def diviser_en_parties(sequence: List[str]) -> List[List[str]]:
    """
    Divise une séquence en parties indépendantes basées sur les marqueurs
    
    Args:
        sequence: Séquence globale avec marqueurs __FIN_PARTIE__
        
    Returns:
        list: Liste des séquences de chaque partie (sans marqueurs)
    """
    if not sequence:
        return []
    
    parties_sequences = []
    partie_courante = []
    
    for element in sequence:
        if element == "__FIN_PARTIE__":
            if partie_courante:
                parties_sequences.append(partie_courante)
                partie_courante = []
        else:
            partie_courante.append(element)
    
    # Ajouter la dernière partie si elle n'est pas vide
    if partie_courante:
        parties_sequences.append(partie_courante)
    
    return parties_sequences


def calculer_autocorrelation(sequence: List[str], max_lag: int = 20) -> dict:
    """
    Calcule l'autocorrélation de la séquence
    
    Args:
        sequence: Séquence à analyser
        max_lag: Nombre maximum de lags à calculer
        
    Returns:
        dict: Coefficients d'autocorrélation
    """
    # Convertir en valeurs numériques pour l'autocorrélation
    sequence_num = convertir_sequence_numerique(sequence)
    
    autocorr = {}
    n = len(sequence_num)
    
    for lag in range(1, min(max_lag + 1, n // 4)):
        # Calcul de l'autocorrélation pour le lag donné
        x1 = sequence_num[:-lag]
        x2 = sequence_num[lag:]
        
        if len(x1) > 0 and len(x2) > 0:
            corr = np.corrcoef(x1, x2)[0, 1]
            autocorr[lag] = corr if not np.isnan(corr) else 0
    
    return autocorr


def calculer_statistiques_base(sequence: List[str]) -> dict:
    """
    Calcule les statistiques de base d'une séquence
    
    Args:
        sequence: Séquence à analyser
        
    Returns:
        dict: Statistiques de base
    """
    if not sequence:
        return {
            'longueur': 0,
            'valeurs_uniques': 0,
            'entropie_shannon': 0.0,
            'distribution': {}
        }
    
    compteur = Counter(sequence)
    valeurs_uniques = list(compteur.keys())
    
    return {
        'longueur': len(sequence),
        'valeurs_uniques': len(valeurs_uniques),
        'entropie_shannon': calculer_entropie_shannon(sequence),
        'distribution': dict(compteur),
        'frequences': {k: v/len(sequence) for k, v in compteur.items()}
    }


def valider_sequence(sequence: List[str]) -> dict:
    """
    Valide une séquence et retourne des informations de diagnostic
    
    Args:
        sequence: Séquence à valider
        
    Returns:
        dict: Résultats de validation
    """
    if not isinstance(sequence, list):
        return {
            'valide': False,
            'erreur': 'La séquence doit être une liste'
        }
    
    if len(sequence) == 0:
        return {
            'valide': False,
            'erreur': 'La séquence est vide'
        }
    
    # Vérifier les types
    types_elements = set(type(x).__name__ for x in sequence)
    if len(types_elements) > 1:
        return {
            'valide': False,
            'erreur': f'Types mixtes détectés: {types_elements}'
        }
    
    # Vérifier les marqueurs
    nb_marqueurs = sequence.count("__FIN_PARTIE__")
    
    return {
        'valide': True,
        'longueur': len(sequence),
        'type_elements': list(types_elements)[0],
        'nb_marqueurs': nb_marqueurs,
        'valeurs_uniques': len(set(sequence)),
        'a_marqueurs': nb_marqueurs > 0
    }


# Fonctions d'export pour compatibilité
__all__ = [
    'nettoyer_marqueurs',
    'convertir_sequence_numerique', 
    'calculer_distribution',
    'calculer_entropie_shannon',
    'diviser_en_parties',
    'calculer_autocorrelation',
    'calculer_statistiques_base',
    'valider_sequence'
]
