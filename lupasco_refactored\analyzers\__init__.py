"""
ANALYZERS MODULE - ANALYSEURS SPÉCIALISÉS
=========================================

Module contenant les analyseurs spécialisés par INDEX
pour l'analyseur Lupasco refactorisé.

Analyseurs prévus :
- index5_analyzer : Analyseur INDEX5 (18 combinaisons)
- index2_index3_analyzer : Analyseur INDEX2_INDEX3 (9 combinaisons)
- index1_index3_analyzer : Analyseur INDEX1_INDEX3 (6 combinaisons)
- lupasco_analyzer : Analyses spécialisées Lupasco

"""

__version__ = "2.0.0"

# Imports des analyseurs disponibles
from .index5_analyzer import Index5Analyzer
from .index2_index3_analyzer import Index2Index3Analyzer
from .index1_index3_analyzer import Index1Index3Analyzer
from .lupasco_analyzer import LupascoAnalyzer

__all__ = [
    'Index5Analyzer',
    'Index2Index3Analyzer',
    'Index1Index3Analyzer',
    'LupascoAnalyzer'
]
