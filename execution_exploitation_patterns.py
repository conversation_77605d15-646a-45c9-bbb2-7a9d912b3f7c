"""
EXÉCUTION EXPLOITATION DES PATTERNS INDEX5
==========================================

Script pour identifier et exploiter les patterns à haut potentiel
de succès détectés dans l'analyse INDEX5.

Basé sur l'analyse de 6,6M+ éléments pour maximiser les chances de succès.

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

import sys
import traceback
import gc
from datetime import datetime

# Ajouter chemins pour imports
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def charger_donnees_et_analyser():
    """Charge les données et effectue l'analyse complète"""
    print("🔍 PHASE 1 : CHARGEMENT ET ANALYSE COMPLÈTE")
    print("=" * 60)
    
    try:
        # Chargement optimisé
        from lupasco_refactored.core.data_loader import DataLoader
        
        fichier_json = "dataset_baccarat_lupasco_20250617_232800.json"
        loader = DataLoader(fichier_json)
        
        print("📂 Chargement streaming...")
        resultats_chargement = loader.charger_donnees(force_streaming=True)
        
        # Extraction INDEX5
        sequence_index5 = resultats_chargement['sequences']['INDEX5']
        sequence_index5_clean = [x for x in sequence_index5 if x != "__FIN_PARTIE__"]
        
        print(f"✅ INDEX5 extrait : {len(sequence_index5_clean):,} éléments")
        
        # Analyse complète avec 80 prédictions
        from lupasco_refactored.analyzers.index5_master_analyzer import Index5MasterAnalyzer
        
        master = Index5MasterAnalyzer()
        print("🔬 Analyse complète en cours...")
        
        resultats_analyse = master.analyser_index5_complet(
            sequence_index5_clean,
            nb_predictions=80,
            generer_rapport=False  # Pas de rapport pour économiser du temps
        )
        
        print("✅ Analyse complète terminée")
        
        return sequence_index5_clean, resultats_analyse
        
    except Exception as e:
        print(f"❌ Erreur phase 1 : {e}")
        traceback.print_exc()
        return None, None

def exploiter_patterns(sequence_index5, resultats_analyse):
    """Exploite les patterns pour identifier les opportunités"""
    print("\n🎯 PHASE 2 : EXPLOITATION DES PATTERNS")
    print("=" * 60)
    
    try:
        from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
        
        exploiter = PatternExploiter()
        
        print("🔍 Identification des patterns exploitables...")
        patterns_exploitables = exploiter.analyser_patterns_exploitables(
            sequence_index5, resultats_analyse
        )
        
        print("\n📊 RÉSULTATS EXPLOITATION :")
        print("-" * 40)
        
        # Afficher patterns Markov haute confiance
        patterns_markov = patterns_exploitables['patterns_markov_haute_confiance']
        print(f"🔗 Patterns Markov haute confiance : {len(patterns_markov)}")
        
        if patterns_markov:
            top_markov = patterns_markov[0]
            print(f"   🎯 Meilleur pattern : {top_markov['confiance']:.1%} de confiance")
            print(f"   📋 Contexte : {' → '.join(top_markov['contexte'][-2:])}")
            print(f"   🔮 Prédiction : {top_markov['prediction']}")
        
        # Afficher patterns cycliques
        patterns_cycles = patterns_exploitables['patterns_cycles_predictibles']
        print(f"📈 Patterns cycliques : {len(patterns_cycles)}")
        
        if patterns_cycles:
            top_cycle = patterns_cycles[0]
            print(f"   🎯 Meilleur cycle : {top_cycle['confiance']:.1%} de confiance")
            print(f"   📏 Longueur : {top_cycle['longueur']} coups")
        
        # Afficher dépendances cachées
        patterns_deps = patterns_exploitables['patterns_dependances_cachees']
        print(f"🔍 Dépendances cachées : {len(patterns_deps)}")
        
        if patterns_deps:
            top_dep = patterns_deps[0]
            print(f"   🎯 Meilleure dépendance : {top_dep['confiance']:.1%} de confiance")
            print(f"   🔗 {top_dep['source']} → (gap {top_dep['gap']}) → {top_dep['cible']}")
            print(f"   📈 Enrichissement : {top_dep['enrichissement']:.1f}x")
        
        # Afficher stratégies
        strategies = patterns_exploitables['strategies_recommandees']
        print(f"\n🚀 STRATÉGIES D'EXPLOITATION : {len(strategies)}")
        print("-" * 40)
        
        for i, strategie in enumerate(strategies, 1):
            print(f"{i}. {strategie['nom']}")
            print(f"   📊 Espérance gain : {strategie['esperance_gain']:.1%}")
            print(f"   ⚠️  Risque : {strategie['risque']:.1%}")
            print(f"   🎯 Action : {strategie['action_recommandee']}")
            print()
        
        # Zones d'opportunité
        zones = patterns_exploitables['zones_opportunite']
        if zones:
            print(f"🌟 ZONES D'OPPORTUNITÉ MAXIMALE : {len(zones)}")
            print("-" * 40)
            
            for i, zone in enumerate(zones, 1):
                print(f"{i}. {zone['nom']}")
                print(f"   🎯 Opportunité : {zone['opportunite']}")
                print(f"   📊 Confiance combinée : {zone['confiance_combinee']:.1%}")
                print(f"   🚀 Action : {zone['action']}")
                print()
        
        return patterns_exploitables, exploiter
        
    except Exception as e:
        print(f"❌ Erreur phase 2 : {e}")
        traceback.print_exc()
        return None, None

def simuler_exploitation_temps_reel(sequence_index5, patterns_exploitables, exploiter):
    """Simule l'exploitation en temps réel sur les derniers coups"""
    print("\n⚡ PHASE 3 : SIMULATION TEMPS RÉEL")
    print("=" * 60)
    
    try:
        # Prendre les 1000 derniers coups pour simulation
        sequence_simulation = sequence_index5[-1000:]
        
        print(f"🎮 Simulation sur {len(sequence_simulation)} derniers coups")
        
        # Simuler exploitation coup par coup
        opportunites_detectees = []
        predictions_reussies = 0
        total_predictions = 0
        
        # Fenêtre glissante de 50 coups
        for i in range(50, len(sequence_simulation) - 1):
            historique = sequence_simulation[i-50:i]
            coup_reel = sequence_simulation[i]
            
            # Prédire prochaine opportunité
            opportunite = exploiter.predire_prochaine_opportunite(
                historique, patterns_exploitables
            )
            
            if opportunite['type'] != 'AUCUNE_OPPORTUNITE':
                total_predictions += 1
                prediction = opportunite['prediction']
                
                if prediction == coup_reel:
                    predictions_reussies += 1
                    opportunites_detectees.append({
                        'position': i,
                        'prediction': prediction,
                        'reel': coup_reel,
                        'confiance': opportunite['confiance'],
                        'succes': True
                    })
                else:
                    opportunites_detectees.append({
                        'position': i,
                        'prediction': prediction,
                        'reel': coup_reel,
                        'confiance': opportunite['confiance'],
                        'succes': False
                    })
        
        # Résultats simulation
        if total_predictions > 0:
            taux_reussite = predictions_reussies / total_predictions
            print(f"\n📊 RÉSULTATS SIMULATION :")
            print(f"   🎯 Prédictions effectuées : {total_predictions}")
            print(f"   ✅ Prédictions réussies : {predictions_reussies}")
            print(f"   📈 Taux de réussite : {taux_reussite:.1%}")
            
            # Analyser par niveau de confiance
            opportunites_haute_conf = [o for o in opportunites_detectees if o['confiance'] >= 0.65]
            if opportunites_haute_conf:
                reussites_haute_conf = sum(1 for o in opportunites_haute_conf if o['succes'])
                taux_haute_conf = reussites_haute_conf / len(opportunites_haute_conf)
                print(f"   🌟 Haute confiance (≥65%) : {taux_haute_conf:.1%} ({reussites_haute_conf}/{len(opportunites_haute_conf)})")
            
            # Gain théorique
            gain_theorique = (taux_reussite - 0.5) * 100  # vs hasard
            print(f"   💰 Gain théorique vs hasard : {gain_theorique:+.1f}%")
            
            if taux_reussite >= 0.60:
                print("   🎉 EXCELLENT : Taux > 60% - Exploitation très rentable !")
            elif taux_reussite >= 0.55:
                print("   ✅ BON : Taux > 55% - Exploitation rentable")
            elif taux_reussite >= 0.50:
                print("   ⚠️  MOYEN : Taux > 50% - Légèrement avantageux")
            else:
                print("   ❌ FAIBLE : Taux < 50% - Révision nécessaire")
        
        return opportunites_detectees
        
    except Exception as e:
        print(f"❌ Erreur phase 3 : {e}")
        traceback.print_exc()
        return []

def generer_guide_exploitation(patterns_exploitables, exploiter, opportunites_simulation):
    """Génère un guide pratique d'exploitation"""
    print("\n📋 PHASE 4 : GÉNÉRATION GUIDE D'EXPLOITATION")
    print("=" * 60)
    
    try:
        # Sauvegarder patterns
        nom_patterns = exploiter.sauvegarder_patterns_exploitables(
            patterns_exploitables, "guide_exploitation"
        )
        
        # Générer rapport
        nom_rapport = exploiter.generer_rapport_exploitation(
            patterns_exploitables, "guide_exploitation"
        )
        
        # Guide pratique supplémentaire
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_guide = f"guide_pratique_exploitation_{timestamp}.txt"
        
        with open(nom_guide, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("GUIDE PRATIQUE D'EXPLOITATION DES PATTERNS INDEX5\n")
            f.write("=" * 80 + "\n\n")
            f.write("🎯 COMMENT UTILISER CE GUIDE :\n")
            f.write("1. Observez les 4 derniers coups INDEX5\n")
            f.write("2. Vérifiez si un pattern Markov correspond\n")
            f.write("3. Si oui, misez selon la prédiction\n")
            f.write("4. Sinon, attendez le prochain pattern\n\n")
            
            # Top 5 patterns les plus fiables
            f.write("🔗 TOP 5 PATTERNS MARKOV À SURVEILLER :\n")
            f.write("-" * 50 + "\n")
            
            patterns_markov = patterns_exploitables['patterns_markov_haute_confiance'][:5]
            for i, pattern in enumerate(patterns_markov, 1):
                f.write(f"{i}. Si les 4 derniers coups sont :\n")
                f.write(f"   {' → '.join(pattern['contexte'])}\n")
                f.write(f"   ALORS miser sur : {pattern['prediction']}\n")
                f.write(f"   Confiance : {pattern['confiance']:.1%}\n")
                f.write(f"   Basé sur {pattern['occurrences']} occurrences\n\n")
            
            # Conseils pratiques
            f.write("💡 CONSEILS PRATIQUES :\n")
            f.write("-" * 30 + "\n")
            f.write("• Ne misez que sur des patterns avec >60% de confiance\n")
            f.write("• Limitez vos mises à 2-5% de votre capital par coup\n")
            f.write("• Arrêtez après 3 échecs consécutifs\n")
            f.write("• Réévaluez les patterns chaque semaine\n\n")
            
            # Résultats simulation
            if opportunites_simulation:
                total_ops = len(opportunites_simulation)
                reussites = sum(1 for o in opportunites_simulation if o['succes'])
                taux = reussites / total_ops if total_ops > 0 else 0
                
                f.write("📊 VALIDATION SUR DONNÉES HISTORIQUES :\n")
                f.write("-" * 40 + "\n")
                f.write(f"Taux de réussite validé : {taux:.1%}\n")
                f.write(f"Basé sur {total_ops} opportunités détectées\n\n")
        
        print(f"📖 Guide pratique généré : {nom_guide}")
        
        return {
            'patterns_json': nom_patterns,
            'rapport_detaille': nom_rapport,
            'guide_pratique': nom_guide
        }
        
    except Exception as e:
        print(f"❌ Erreur phase 4 : {e}")
        traceback.print_exc()
        return {}

def main():
    """Fonction principale d'exploitation des patterns"""
    print("🎯 EXPLOITATION DES PATTERNS INDEX5 - MAXIMISATION DU SUCCÈS")
    print("=" * 80)
    print("Expert Statisticien IA - Identification patterns à haut potentiel")
    print(f"📅 Date : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Phase 1: Chargement et analyse
    sequence_index5, resultats_analyse = charger_donnees_et_analyser()
    if not sequence_index5 or not resultats_analyse:
        print("❌ Échec phase 1")
        return False
    
    # Phase 2: Exploitation patterns
    patterns_exploitables, exploiter = exploiter_patterns(sequence_index5, resultats_analyse)
    if not patterns_exploitables:
        print("❌ Échec phase 2")
        return False
    
    # Phase 3: Simulation temps réel
    opportunites_simulation = simuler_exploitation_temps_reel(
        sequence_index5, patterns_exploitables, exploiter
    )
    
    # Phase 4: Guide d'exploitation
    fichiers_generes = generer_guide_exploitation(
        patterns_exploitables, exploiter, opportunites_simulation
    )
    
    # Résumé final
    print("\n" + "=" * 80)
    print("🎉 EXPLOITATION DES PATTERNS TERMINÉE AVEC SUCCÈS")
    print("=" * 80)
    
    nb_patterns_markov = len(patterns_exploitables['patterns_markov_haute_confiance'])
    nb_strategies = len(patterns_exploitables['strategies_recommandees'])
    nb_zones = len(patterns_exploitables['zones_opportunite'])
    
    print(f"🔗 Patterns Markov exploitables : {nb_patterns_markov}")
    print(f"🚀 Stratégies d'exploitation : {nb_strategies}")
    print(f"🌟 Zones d'opportunité : {nb_zones}")
    
    if opportunites_simulation:
        total_ops = len(opportunites_simulation)
        reussites = sum(1 for o in opportunites_simulation if o['succes'])
        taux = reussites / total_ops
        print(f"📊 Taux de réussite validé : {taux:.1%}")
    
    print("\n📁 FICHIERS GÉNÉRÉS :")
    for nom, fichier in fichiers_generes.items():
        print(f"   📄 {nom} : {fichier}")
    
    print("\n🎯 PRÊT POUR EXPLOITATION EN CONDITIONS RÉELLES !")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
