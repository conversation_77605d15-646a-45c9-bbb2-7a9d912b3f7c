"""
TEST EXCLUSION TIE DANS LES PATTERNS MARKOVIENS
===============================================

Test pour vérifier que les patterns Markoviens n'incluent plus
aucune séquence contenant des TIE.

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

import sys
sys.path.append('.')
sys.path.append('./lupasco_refactored')

def test_exclusion_tie_patterns():
    """Test l'exclusion des TIE dans les patterns"""
    print("🔍 TEST EXCLUSION TIE DANS LES PATTERNS MARKOVIENS")
    print("=" * 60)
    
    # Créer séquence de test avec TIE
    sequence_test = [
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER', 
        'SYNC_pair_4_TIE',        # ← TIE à exclure
        'SYNC_impair_5_PLAYER',
        'DESYNC_pair_6_BANKER',
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_TIE',      # ← TIE à exclure
        'SYNC_impair_5_BANKER',
        'DESYNC_pair_6_PLAYER',
        'SYNC_pair_4_BANKER',
        'DESYNC_pair_4_PLAYER',
        'SYNC_impair_5_PLAYER'
    ]
    
    print(f"📊 Séquence test : {len(sequence_test)} éléments")
    print(f"🚫 TIE dans séquence : {sum(1 for x in sequence_test if 'TIE' in x)}")
    
    # Tester avec PatternExploiter
    try:
        from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
        
        exploiter = PatternExploiter()
        
        # Configurer seuils très bas pour détecter des patterns
        exploiter.seuil_confiance = 0.10  # 10%
        exploiter.min_occurrences_markov = 1  # 1 occurrence minimum
        
        print(f"\n⚙️  Configuration test :")
        print(f"   🎯 Seuil confiance : {exploiter.seuil_confiance:.0%}")
        print(f"   📊 Min. occurrences : {exploiter.min_occurrences_markov}")
        
        # Analyser patterns
        patterns = exploiter._extraire_patterns_markov_exploitables(sequence_test, {})
        
        print(f"\n📋 RÉSULTATS :")
        print(f"   🔗 Patterns détectés : {len(patterns)}")
        
        # Vérifier qu'aucun pattern ne contient de TIE
        patterns_avec_tie = []
        for pattern in patterns:
            contexte = pattern['contexte']
            prediction = pattern['prediction']
            
            # Vérifier contexte
            for element in contexte:
                if 'TIE' in element:
                    patterns_avec_tie.append(f"Contexte: {element}")
            
            # Vérifier prédiction
            if 'TIE' in prediction:
                patterns_avec_tie.append(f"Prédiction: {prediction}")
        
        if patterns_avec_tie:
            print(f"   ❌ ÉCHEC : {len(patterns_avec_tie)} patterns avec TIE détectés !")
            for pattern_tie in patterns_avec_tie:
                print(f"      🚨 {pattern_tie}")
            return False
        else:
            print(f"   ✅ SUCCÈS : Aucun pattern avec TIE détecté")
            
            # Afficher quelques patterns pour vérification
            if patterns:
                print(f"\n📋 EXEMPLES PATTERNS (sans TIE) :")
                for i, pattern in enumerate(patterns[:3], 1):
                    contexte_str = ' → '.join(pattern['contexte'][-2:])
                    print(f"   {i}. {contexte_str} → {pattern['prediction']}")
                    print(f"      Confiance: {pattern['confiance']:.1%}")
            
            return True
        
    except Exception as e:
        print(f"❌ Erreur test : {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exclusion_tie_cycles():
    """Test l'exclusion des TIE dans les cycles"""
    print("\n🔍 TEST EXCLUSION TIE DANS LES CYCLES")
    print("=" * 60)
    
    # Séquence avec cycle contenant TIE
    sequence_cycle = [
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER',
        'SYNC_pair_4_TIE',        # ← TIE dans cycle
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER',
        'SYNC_pair_4_TIE',        # ← Répétition cycle avec TIE
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER',
        'SYNC_impair_5_PLAYER',   # ← Cycle sans TIE
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER',
        'SYNC_impair_5_PLAYER'    # ← Répétition cycle sans TIE
    ]
    
    try:
        from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
        
        exploiter = PatternExploiter()
        exploiter.seuil_cycles = 0.10  # Très bas pour test
        
        # Tester cycles de longueur 3
        cycles = exploiter._detecter_cycles_longueur(sequence_cycle, 3)
        
        print(f"📋 Cycles longueur 3 détectés : {len(cycles)}")
        
        # Vérifier qu'aucun cycle ne contient TIE
        cycles_avec_tie = []
        for cycle in cycles:
            pattern_cycle = cycle['pattern']
            for element in pattern_cycle:
                if 'TIE' in element:
                    cycles_avec_tie.append(pattern_cycle)
                    break
        
        if cycles_avec_tie:
            print(f"   ❌ ÉCHEC : {len(cycles_avec_tie)} cycles avec TIE !")
            return False
        else:
            print(f"   ✅ SUCCÈS : Aucun cycle avec TIE")
            
            # Afficher cycles détectés
            for i, cycle in enumerate(cycles, 1):
                pattern_str = ' → '.join(cycle['pattern'])
                print(f"   {i}. {pattern_str} ({cycle['repetitions']} rép.)")
            
            return True
        
    except Exception as e:
        print(f"❌ Erreur test cycles : {e}")
        return False

def test_exclusion_tie_dependances():
    """Test l'exclusion des TIE dans les dépendances"""
    print("\n🔍 TEST EXCLUSION TIE DANS LES DÉPENDANCES")
    print("=" * 60)
    
    # Séquence avec dépendances incluant TIE
    sequence_deps = [
        'SYNC_pair_4_TIE',        # ← Source TIE
        'SYNC_pair_4_PLAYER',
        'DESYNC_pair_4_BANKER',   # ← Cible (gap 2)
        'SYNC_pair_4_PLAYER',     # ← Source normale
        'DESYNC_pair_4_BANKER',
        'SYNC_pair_4_TIE',        # ← Cible TIE (gap 2)
        'SYNC_pair_4_PLAYER',     # ← Source normale
        'DESYNC_pair_4_BANKER',
        'SYNC_impair_5_PLAYER'    # ← Cible normale (gap 2)
    ]
    
    try:
        from lupasco_refactored.strategies.pattern_exploitation import PatternExploiter
        
        exploiter = PatternExploiter()
        exploiter.seuil_dependances = 0.10  # Très bas pour test
        exploiter.min_occurrences_deps = 1   # 1 minimum pour test
        
        # Tester dépendances gap 2
        dependances = exploiter._analyser_dependances_gap(sequence_deps, 2)
        
        print(f"📋 Dépendances gap 2 détectées : {len(dependances)}")
        
        # Vérifier qu'aucune dépendance ne contient TIE
        deps_avec_tie = []
        for dep in dependances:
            source = dep['source']
            cible = dep['cible']
            
            if 'TIE' in source or 'TIE' in cible:
                deps_avec_tie.append(f"{source} → {cible}")
        
        if deps_avec_tie:
            print(f"   ❌ ÉCHEC : {len(deps_avec_tie)} dépendances avec TIE !")
            for dep_tie in deps_avec_tie:
                print(f"      🚨 {dep_tie}")
            return False
        else:
            print(f"   ✅ SUCCÈS : Aucune dépendance avec TIE")
            
            # Afficher dépendances détectées
            for i, dep in enumerate(dependances, 1):
                print(f"   {i}. {dep['source']} → {dep['cible']} (gap {dep['gap']})")
                print(f"      Enrichissement: {dep['enrichissement']:.1f}x")
            
            return True
        
    except Exception as e:
        print(f"❌ Erreur test dépendances : {e}")
        return False

def main():
    """Test principal d'exclusion TIE"""
    print("🚫 TEST COMPLET EXCLUSION TIE DANS LES PATTERNS")
    print("=" * 80)
    print("Vérification que les TIE sont complètement exclus de l'analyse")
    print("=" * 80)
    
    tests_reussis = 0
    total_tests = 3
    
    # Test 1: Patterns Markoviens
    if test_exclusion_tie_patterns():
        tests_reussis += 1
    
    # Test 2: Cycles
    if test_exclusion_tie_cycles():
        tests_reussis += 1
    
    # Test 3: Dépendances
    if test_exclusion_tie_dependances():
        tests_reussis += 1
    
    # Résumé
    print("\n" + "=" * 80)
    print("📊 RÉSUMÉ TEST EXCLUSION TIE")
    print("=" * 80)
    print(f"✅ Tests réussis : {tests_reussis}/{total_tests}")
    print(f"📈 Taux de réussite : {(tests_reussis/total_tests)*100:.1f}%")
    
    if tests_reussis == total_tests:
        print("🎉 EXCLUSION TIE COMPLÈTEMENT FONCTIONNELLE !")
        print("✅ Aucun pattern ne contient plus de TIE")
        print("🎯 Analyse focalisée sur PLAYER/BANKER uniquement")
    else:
        print("⚠️  Exclusion TIE incomplète")
        print("🔧 Vérifiez les corrections nécessaires")
    
    print("=" * 80)
    
    return tests_reussis == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
