"""
INDEX5 MASTER ANALYZER
======================

Analyseur maître intégrant tous les modules INDEX5 avancés
Implémente l'ensemble des recommandations de base.txt :

1. Entropie Multi-Échelle fractale
2. Modélisation Markovienne d'ordre supérieur  
3. Analyse spectrale des fréquences caractéristiques
4. Tests de stationnarité adaptés INDEX5
5. Détection d'anomalies avec écarts de fragmentation
6. Prédiction adaptative avec patterns autocorrélatifs
7. Optimisation entropique

Auteur : Expert Statisticien IA
Date : 2025-06-20
Version : 1.0
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any
import json
from datetime import datetime

from lupasco_refactored.statistics.index5_advanced_entropy import Index5AdvancedEntropy
from lupasco_refactored.statistics.index5_anomaly_detection import Index5AnomalyDetector
from lupasco_refactored.statistics.index5_adaptive_prediction import Index5AdaptivePredictor
from lupasco_refactored.utils.data_utils import nettoyer_marqueurs


class Index5MasterAnalyzer:
    """
    Analyseur maître INDEX5 - Intégration complète
    
    Coordonne tous les modules spécialisés INDEX5 :
    - Entropie avancée
    - Détection d'anomalies
    - Prédiction adaptative
    - Synthèse globale
    """
    
    def __init__(self):
        """Initialise l'analyseur maître INDEX5"""
        self.entropy_analyzer = Index5AdvancedEntropy()
        self.anomaly_detector = Index5AnomalyDetector()
        self.adaptive_predictor = Index5AdaptivePredictor()
        
        self.version = "1.0"
        self.date_creation = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def analyser_index5_complet(self, sequence_index5: List[str], 
                               nb_predictions: int = 10,
                               generer_rapport: bool = True) -> Dict:
        """
        Analyse INDEX5 complète selon toutes les recommandations base.txt
        
        Args:
            sequence_index5: Séquence INDEX5 brute avec marqueurs
            nb_predictions: Nombre de prédictions à générer
            generer_rapport: Générer un rapport détaillé
            
        Returns:
            dict: Résultats complets de l'analyse INDEX5 avancée
        """
        print("\n" + "="*80)
        print("🎓 ANALYSE INDEX5 MAÎTRE - RECOMMANDATIONS BASE.TXT COMPLÈTES")
        print("="*80)
        print(f"📊 Séquence : {len(sequence_index5)} éléments")
        print(f"🔮 Prédictions : {nb_predictions}")
        print(f"📅 Date : {self.date_creation}")
        print("="*80)
        
        # Nettoyer la séquence
        sequence_propre = nettoyer_marqueurs(sequence_index5)
        
        if len(sequence_propre) < 100:
            return {
                'erreur': 'Séquence INDEX5 trop courte pour analyse complète',
                'taille_minimum': 100,
                'taille_actuelle': len(sequence_propre)
            }
        
        resultats_complets = {
            'metadata': {
                'version_analyseur': self.version,
                'date_analyse': self.date_creation,
                'taille_sequence': len(sequence_propre),
                'nb_predictions_demandees': nb_predictions
            }
        }
        
        try:
            # 1. ANALYSE ENTROPIQUE AVANCÉE
            print("\n🔬 PHASE 1 : ANALYSE ENTROPIQUE AVANCÉE INDEX5")
            print("-" * 50)
            resultats_entropie = self.entropy_analyzer.analyser_index5_complet(sequence_propre)
            resultats_complets['analyse_entropique_avancee'] = resultats_entropie
            
            # 2. DÉTECTION D'ANOMALIES
            print("\n🚨 PHASE 2 : DÉTECTION ANOMALIES INDEX5")
            print("-" * 50)
            resultats_anomalies = self.anomaly_detector.detecter_anomalies_index5(sequence_propre)
            resultats_complets['detection_anomalies'] = resultats_anomalies
            
            # 3. PRÉDICTION ADAPTATIVE
            print("\n🔮 PHASE 3 : PRÉDICTION ADAPTATIVE INDEX5")
            print("-" * 50)
            resultats_prediction = self.adaptive_predictor.predire_index5_adaptatif(
                sequence_propre, nb_predictions
            )
            resultats_complets['prediction_adaptative'] = resultats_prediction
            
            # 4. SYNTHÈSE GLOBALE
            print("\n📋 PHASE 4 : SYNTHÈSE GLOBALE INDEX5")
            print("-" * 50)
            synthese_globale = self._synthetiser_analyse_complete(resultats_complets)
            resultats_complets['synthese_globale'] = synthese_globale
            
            # 5. GÉNÉRATION RAPPORT
            if generer_rapport:
                print("\n📄 PHASE 5 : GÉNÉRATION RAPPORT INDEX5")
                print("-" * 50)
                rapport = self._generer_rapport_complet(resultats_complets)
                resultats_complets['rapport_detaille'] = rapport
            
            print("\n" + "="*80)
            print("✅ ANALYSE INDEX5 MAÎTRE TERMINÉE AVEC SUCCÈS")
            print("="*80)
            
        except Exception as e:
            print(f"\n❌ ERREUR ANALYSE INDEX5 : {str(e)}")
            resultats_complets['erreur_analyse'] = str(e)
        
        return resultats_complets
    
    def _synthetiser_analyse_complete(self, resultats: Dict) -> Dict:
        """Synthèse globale de tous les résultats INDEX5"""
        synthese = {
            'scores_globaux': {},
            'classification_index5': 'NORMAL',
            'niveau_complexite': 'MODÉRÉ',
            'niveau_risque': 'FAIBLE',
            'recommandations_prioritaires': [],
            'metriques_cles': {},
            'resume_executif': ''
        }
        
        # Extraction des scores principaux
        try:
            # Score entropique
            if 'analyse_entropique_avancee' in resultats:
                entropie_data = resultats['analyse_entropique_avancee']
                if 'synthese_index5' in entropie_data:
                    synthese['scores_globaux']['complexite'] = entropie_data['synthese_index5'].get('score_complexite_globale', 0)
                    synthese['niveau_complexite'] = entropie_data['synthese_index5'].get('classification_index5', 'NORMAL')
            
            # Score anomalies
            if 'detection_anomalies' in resultats:
                anomalies_data = resultats['detection_anomalies']
                synthese['scores_globaux']['anomalies'] = anomalies_data.get('score_anomalie_global', 0)
                synthese['niveau_risque'] = anomalies_data.get('classification_risque', 'NORMAL')
            
            # Score prédiction
            if 'prediction_adaptative' in resultats:
                prediction_data = resultats['prediction_adaptative']
                if 'performance_adaptative' in prediction_data:
                    perf = prediction_data['performance_adaptative'].get('performance_moyenne', 0)
                    synthese['scores_globaux']['prediction'] = perf * 100
            
        except Exception as e:
            print(f"Erreur extraction scores : {e}")
        
        # Classification globale INDEX5
        score_complexite = synthese['scores_globaux'].get('complexite', 0)
        score_anomalies = synthese['scores_globaux'].get('anomalies', 0)
        score_prediction = synthese['scores_globaux'].get('prediction', 0)
        
        score_global = (score_complexite + score_anomalies + score_prediction) / 3
        synthese['scores_globaux']['global'] = score_global
        
        if score_global < 30:
            synthese['classification_index5'] = 'SIMPLE'
        elif score_global < 50:
            synthese['classification_index5'] = 'NORMAL'
        elif score_global < 70:
            synthese['classification_index5'] = 'COMPLEXE'
        else:
            synthese['classification_index5'] = 'TRÈS_COMPLEXE'
        
        # Recommandations prioritaires
        recommandations = []
        
        if score_complexite > 70:
            recommandations.append("PRIORITÉ 1: Système INDEX5 hautement complexe - Surveillance continue recommandée")
        
        if score_anomalies > 50:
            recommandations.append("PRIORITÉ 2: Anomalies détectées - Investigation approfondie nécessaire")
        
        if synthese['niveau_risque'] in ['RISQUE_ÉLEVÉ', 'RISQUE_CRITIQUE']:
            recommandations.append("PRIORITÉ 3: Niveau de risque élevé - Mesures correctives immédiates")
        
        if score_prediction < 30:
            recommandations.append("PRIORITÉ 4: Prédictibilité faible - Amélioration des modèles nécessaire")
        
        # Ajouter recommandations spécifiques des modules
        try:
            if 'analyse_entropique_avancee' in resultats:
                reco_entropie = resultats['analyse_entropique_avancee'].get('synthese_index5', {}).get('recommandations', [])
                recommandations.extend(reco_entropie)
            
            if 'detection_anomalies' in resultats:
                reco_anomalies = resultats['detection_anomalies'].get('recommandations_action', [])
                recommandations.extend(reco_anomalies)
        except:
            pass
        
        synthese['recommandations_prioritaires'] = recommandations[:10]  # Top 10
        
        # Métriques clés
        synthese['metriques_cles'] = {
            'score_complexite_globale': score_complexite,
            'score_anomalies_globale': score_anomalies,
            'score_prediction_globale': score_prediction,
            'score_synthese_globale': score_global,
            'nb_anomalies_detectees': 0,
            'nb_recommandations': len(recommandations)
        }
        
        try:
            if 'detection_anomalies' in resultats:
                synthese['metriques_cles']['nb_anomalies_detectees'] = resultats['detection_anomalies'].get('nb_anomalies_total', 0)
        except:
            pass
        
        # Résumé exécutif
        synthese['resume_executif'] = self._generer_resume_executif(synthese)
        
        return synthese
    
    def _generer_resume_executif(self, synthese: Dict) -> str:
        """Génère un résumé exécutif de l'analyse INDEX5"""
        classification = synthese.get('classification_index5', 'NORMAL')
        score_global = synthese.get('scores_globaux', {}).get('global', 0)
        niveau_risque = synthese.get('niveau_risque', 'NORMAL')
        nb_anomalies = synthese.get('metriques_cles', {}).get('nb_anomalies_detectees', 0)
        
        resume = f"""
RÉSUMÉ EXÉCUTIF - ANALYSE INDEX5 MAÎTRE

Classification Système : {classification}
Score Global : {score_global:.1f}/100
Niveau de Risque : {niveau_risque}
Anomalies Détectées : {nb_anomalies}

ÉVALUATION :
Le système INDEX5 présente une complexité {classification.lower()} avec un score global de {score_global:.1f}/100.
"""
        
        if score_global > 70:
            resume += "⚠️  ATTENTION : Système hautement complexe nécessitant une surveillance renforcée.\n"
        elif score_global > 50:
            resume += "ℹ️  INFORMATION : Système modérément complexe, surveillance standard recommandée.\n"
        else:
            resume += "✅ NORMAL : Système dans les paramètres normaux.\n"
        
        if nb_anomalies > 0:
            resume += f"🚨 {nb_anomalies} anomalie(s) détectée(s) - Investigation recommandée.\n"
        
        nb_reco = len(synthese.get('recommandations_prioritaires', []))
        if nb_reco > 0:
            resume += f"📋 {nb_reco} recommandation(s) d'action disponible(s).\n"
        
        return resume.strip()
    
    def _generer_rapport_complet(self, resultats: Dict) -> Dict:
        """Génère un rapport détaillé de l'analyse INDEX5"""
        rapport = {
            'titre': 'RAPPORT COMPLET ANALYSE INDEX5 MAÎTRE',
            'date_generation': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'sections': {}
        }
        
        # Section 1: Résumé exécutif
        if 'synthese_globale' in resultats:
            rapport['sections']['resume_executif'] = {
                'titre': '1. RÉSUMÉ EXÉCUTIF',
                'contenu': resultats['synthese_globale'].get('resume_executif', ''),
                'metriques_cles': resultats['synthese_globale'].get('metriques_cles', {})
            }
        
        # Section 2: Analyse entropique
        if 'analyse_entropique_avancee' in resultats:
            entropie_data = resultats['analyse_entropique_avancee']
            rapport['sections']['analyse_entropique'] = {
                'titre': '2. ANALYSE ENTROPIQUE AVANCÉE',
                'entropie_multi_echelle': entropie_data.get('entropie_multi_echelle', {}),
                'markov_ordre_superieur': entropie_data.get('markov_ordre_superieur', {}),
                'analyse_spectrale': entropie_data.get('analyse_spectrale', {}),
                'tests_stationnarite': entropie_data.get('tests_stationnarite', {})
            }
        
        # Section 3: Détection anomalies
        if 'detection_anomalies' in resultats:
            anomalies_data = resultats['detection_anomalies']
            rapport['sections']['detection_anomalies'] = {
                'titre': '3. DÉTECTION D\'ANOMALIES',
                'score_global': anomalies_data.get('score_anomalie_global', 0),
                'classification_risque': anomalies_data.get('classification_risque', 'NORMAL'),
                'nb_anomalies': anomalies_data.get('nb_anomalies_total', 0),
                'anomalies_critiques': anomalies_data.get('nb_anomalies_critiques', 0),
                'recommandations': anomalies_data.get('recommandations_action', [])
            }
        
        # Section 4: Prédiction adaptative
        if 'prediction_adaptative' in resultats:
            prediction_data = resultats['prediction_adaptative']
            rapport['sections']['prediction_adaptative'] = {
                'titre': '4. PRÉDICTION ADAPTATIVE',
                'nb_predictions': len(prediction_data.get('predictions', [])),
                'predictions': prediction_data.get('predictions', [])[:5],  # Top 5
                'confiance_moyenne': np.mean(prediction_data.get('confiance_predictions', [])) if prediction_data.get('confiance_predictions') else 0,
                'performance': prediction_data.get('performance_adaptative', {})
            }
        
        # Section 5: Recommandations
        if 'synthese_globale' in resultats:
            rapport['sections']['recommandations'] = {
                'titre': '5. RECOMMANDATIONS PRIORITAIRES',
                'recommandations': resultats['synthese_globale'].get('recommandations_prioritaires', []),
                'actions_immediates': [r for r in resultats['synthese_globale'].get('recommandations_prioritaires', []) if 'PRIORITÉ 1' in r or 'PRIORITÉ 2' in r]
            }
        
        return rapport
    
    def sauvegarder_resultats(self, resultats: Dict, nom_fichier: str = None) -> str:
        """Sauvegarde les résultats de l'analyse INDEX5"""
        if nom_fichier is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"analyse_index5_maitre_{timestamp}.json"
        
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                json.dump(resultats, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ Résultats sauvegardés : {nom_fichier}")
            return nom_fichier
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde : {e}")
            return ""
    
    def generer_rapport_texte(self, resultats: Dict, nom_fichier: str = None) -> str:
        """Génère un rapport texte lisible"""
        if nom_fichier is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"rapport_index5_maitre_{timestamp}.txt"
        
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                f.write("="*80 + "\n")
                f.write("RAPPORT ANALYSE INDEX5 MAÎTRE - RECOMMANDATIONS BASE.TXT\n")
                f.write("="*80 + "\n\n")
                
                # Métadonnées
                if 'metadata' in resultats:
                    metadata = resultats['metadata']
                    f.write(f"Date d'analyse : {metadata.get('date_analyse', 'N/A')}\n")
                    f.write(f"Version analyseur : {metadata.get('version_analyseur', 'N/A')}\n")
                    f.write(f"Taille séquence : {metadata.get('taille_sequence', 'N/A')} éléments\n\n")
                
                # Synthèse globale
                if 'synthese_globale' in resultats:
                    synthese = resultats['synthese_globale']
                    f.write("SYNTHÈSE GLOBALE\n")
                    f.write("-" * 20 + "\n")
                    f.write(synthese.get('resume_executif', '') + "\n\n")
                    
                    # Métriques clés
                    if 'metriques_cles' in synthese:
                        f.write("MÉTRIQUES CLÉS\n")
                        f.write("-" * 15 + "\n")
                        for metric, value in synthese['metriques_cles'].items():
                            f.write(f"{metric}: {value}\n")
                        f.write("\n")
                    
                    # Recommandations
                    if 'recommandations_prioritaires' in synthese:
                        f.write("RECOMMANDATIONS PRIORITAIRES\n")
                        f.write("-" * 30 + "\n")
                        for i, reco in enumerate(synthese['recommandations_prioritaires'], 1):
                            f.write(f"{i}. {reco}\n")
                        f.write("\n")
                
                # Rapport détaillé
                if 'rapport_detaille' in resultats:
                    rapport = resultats['rapport_detaille']
                    if 'sections' in rapport:
                        for section_key, section_data in rapport['sections'].items():
                            f.write(f"{section_data.get('titre', section_key.upper())}\n")
                            f.write("=" * len(section_data.get('titre', section_key)) + "\n")
                            
                            # Contenu spécifique par section
                            if section_key == 'resume_executif':
                                f.write(section_data.get('contenu', '') + "\n\n")
                            elif section_key == 'detection_anomalies':
                                f.write(f"Score global anomalies : {section_data.get('score_global', 0):.2f}\n")
                                f.write(f"Classification risque : {section_data.get('classification_risque', 'N/A')}\n")
                                f.write(f"Nombre d'anomalies : {section_data.get('nb_anomalies', 0)}\n\n")
                            elif section_key == 'prediction_adaptative':
                                f.write(f"Nombre de prédictions : {section_data.get('nb_predictions', 0)}\n")
                                f.write(f"Confiance moyenne : {section_data.get('confiance_moyenne', 0):.3f}\n")
                                if section_data.get('predictions'):
                                    f.write("Prédictions (top 5) :\n")
                                    for i, pred in enumerate(section_data['predictions'], 1):
                                        f.write(f"  {i}. {pred}\n")
                                f.write("\n")
                            
                            f.write("\n")
            
            print(f"✅ Rapport texte généré : {nom_fichier}")
            return nom_fichier
            
        except Exception as e:
            print(f"❌ Erreur génération rapport : {e}")
            return ""


# Fonctions d'export pour compatibilité
__all__ = [
    'Index5MasterAnalyzer'
]
