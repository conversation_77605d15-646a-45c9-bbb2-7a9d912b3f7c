"""
INTÉGRATION MODULES INDEX5 AVEC ANALYSEUR PRINCIPAL
===================================================

Code d'intégration pour ajouter les modules INDEX5 avancés
à l'analyseur principal (AnalyseurSequencesLupasco).

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

def ajouter_methodes_index5_avancees():
    """
    Code à ajouter dans la classe AnalyseurSequencesLupasco
    pour intégrer les modules INDEX5 avancés
    """
    
    code_integration = '''
    # ================================================================
    # MÉTHODES INDEX5 AVANCÉES - RECOMMANDATIONS BASE.TXT
    # ================================================================
    
    def analyser_index5_avec_modules_avances(self, nb_predictions=10, generer_rapport=True):
        """
        Analyse INDEX5 complète avec modules avancés
        Implémente toutes les recommandations de base.txt :
        - Entropie Multi-Échelle fractale
        - Modélisation Markovienne d'ordre supérieur
        - Analyse spectrale des fréquences caractéristiques
        - Tests de stationnarité adaptés INDEX5
        - Détection d'anomalies avec écarts de fragmentation
        - Prédiction adaptative avec patterns autocorrélatifs
        
        Args:
            nb_predictions: Nombre de prédictions à générer
            generer_rapport: Générer un rapport détaillé
            
        Returns:
            dict: Résultats complets de l'analyse INDEX5 avancée
        """
        print("\\n🎓 ANALYSE INDEX5 AVANCÉE - MODULES SPÉCIALISÉS")
        print("=" * 60)
        
        try:
            # Vérifier que INDEX5 est disponible
            if 'INDEX5' not in self.sequences:
                return {'erreur': 'Séquence INDEX5 non disponible'}
            
            sequence_index5 = self.sequences['INDEX5']
            
            # Importer et utiliser l'analyseur maître
            from lupasco_refactored.analyzers.index5_master_analyzer import Index5MasterAnalyzer
            
            master_analyzer = Index5MasterAnalyzer()
            resultats = master_analyzer.analyser_index5_complet(
                sequence_index5,
                nb_predictions=nb_predictions,
                generer_rapport=generer_rapport
            )
            
            # Sauvegarder automatiquement les résultats
            if 'erreur' not in resultats:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # Sauvegarde JSON
                nom_json = f"analyse_index5_avancee_{timestamp}.json"
                master_analyzer.sauvegarder_resultats(resultats, nom_json)
                
                # Rapport texte
                nom_rapport = f"rapport_index5_avancee_{timestamp}.txt"
                master_analyzer.generer_rapport_texte(resultats, nom_rapport)
                
                resultats['fichiers_generes'] = {
                    'json': nom_json,
                    'rapport': nom_rapport
                }
            
            return resultats
            
        except Exception as e:
            print(f"❌ Erreur analyse INDEX5 avancée : {e}")
            return {'erreur': str(e)}
    
    def analyser_index5_entropie_avancee(self):
        """
        Analyse entropique avancée INDEX5 uniquement
        """
        try:
            if 'INDEX5' not in self.sequences:
                return {'erreur': 'Séquence INDEX5 non disponible'}
            
            from lupasco_refactored.statistics.index5_advanced_entropy import Index5AdvancedEntropy
            
            analyzer = Index5AdvancedEntropy()
            return analyzer.analyser_index5_complet(self.sequences['INDEX5'])
            
        except Exception as e:
            return {'erreur': str(e)}
    
    def analyser_index5_anomalies(self):
        """
        Détection d'anomalies INDEX5 uniquement
        """
        try:
            if 'INDEX5' not in self.sequences:
                return {'erreur': 'Séquence INDEX5 non disponible'}
            
            from lupasco_refactored.statistics.index5_anomaly_detection import Index5AnomalyDetector
            
            detector = Index5AnomalyDetector()
            return detector.detecter_anomalies_index5(self.sequences['INDEX5'])
            
        except Exception as e:
            return {'erreur': str(e)}
    
    def analyser_index5_prediction_adaptative(self, nb_predictions=10):
        """
        Prédiction adaptative INDEX5 uniquement
        """
        try:
            if 'INDEX5' not in self.sequences:
                return {'erreur': 'Séquence INDEX5 non disponible'}
            
            from lupasco_refactored.statistics.index5_adaptive_prediction import Index5AdaptivePredictor
            
            predictor = Index5AdaptivePredictor()
            return predictor.predire_index5_adaptatif(self.sequences['INDEX5'], nb_predictions)
            
        except Exception as e:
            return {'erreur': str(e)}
    '''
    
    return code_integration

def modifier_analyser_sequences_completes():
    """
    Code à ajouter dans la méthode analyser_sequences_completes()
    pour intégrer automatiquement l'analyse INDEX5 avancée
    """
    
    code_modification = '''
    # AJOUTER APRÈS L'ANALYSE INDEX5 EXISTANTE :
    # ==========================================
    
    # Phase bonus : Analyse INDEX5 avancée
    if 'INDEX5' in self.sequences:
        print("\\n🎓 PHASE BONUS : ANALYSE INDEX5 AVANCÉE")
        print("-" * 50)
        
        try:
            resultats_avances = self.analyser_index5_avec_modules_avances(
                nb_predictions=10,
                generer_rapport=True
            )
            
            if 'erreur' not in resultats_avances:
                resultats['analyse_index5_avancee'] = resultats_avances
                
                # Afficher synthèse
                if 'synthese_globale' in resultats_avances:
                    synthese = resultats_avances['synthese_globale']
                    print(f"   📊 Classification INDEX5 : {synthese.get('classification_index5', 'N/A')}")
                    print(f"   📈 Score global : {synthese.get('scores_globaux', {}).get('global', 0):.1f}/100")
                    print(f"   ⚠️  Niveau risque : {synthese.get('niveau_risque', 'N/A')}")
                    
                    nb_reco = len(synthese.get('recommandations_prioritaires', []))
                    print(f"   📋 Recommandations : {nb_reco}")
                
                print("   ✅ Analyse INDEX5 avancée terminée")
            else:
                print(f"   ❌ Erreur analyse avancée : {resultats_avances['erreur']}")
                
        except Exception as e:
            print(f"   ❌ Erreur inattendue analyse avancée : {e}")
    '''
    
    return code_modification

def instructions_integration():
    """
    Instructions complètes pour l'intégration
    """
    
    instructions = '''
    INSTRUCTIONS D'INTÉGRATION - MODULES INDEX5 AVANCÉS
    ===================================================
    
    1. AJOUTER LES MÉTHODES À LA CLASSE AnalyseurSequencesLupasco
    -------------------------------------------------------------
    
    Copier le code de ajouter_methodes_index5_avancees() dans la classe
    AnalyseurSequencesLupasco (vers la ligne 2800, après les autres méthodes).
    
    2. MODIFIER LA MÉTHODE analyser_sequences_completes()
    ----------------------------------------------------
    
    Ajouter le code de modifier_analyser_sequences_completes() à la fin
    de la méthode analyser_sequences_completes(), juste avant le return.
    
    3. UTILISATION
    --------------
    
    # Analyse complète automatique (recommandé)
    analyseur = AnalyseurSequencesLupasco("dataset.json")
    analyseur.charger_donnees()
    resultats = analyseur.analyser_sequences_completes()
    # → Inclut automatiquement l'analyse INDEX5 avancée
    
    # Analyse INDEX5 avancée séparée
    resultats_avances = analyseur.analyser_index5_avec_modules_avances()
    
    # Analyses spécialisées
    entropie = analyseur.analyser_index5_entropie_avancee()
    anomalies = analyseur.analyser_index5_anomalies()
    predictions = analyseur.analyser_index5_prediction_adaptative(nb_predictions=5)
    
    4. FICHIERS GÉNÉRÉS AUTOMATIQUEMENT
    -----------------------------------
    
    - analyse_index5_avancee_YYYYMMDD_HHMMSS.json (résultats complets)
    - rapport_index5_avancee_YYYYMMDD_HHMMSS.txt (rapport lisible)
    
    5. COMPATIBILITÉ
    ----------------
    
    ✅ Compatible avec la structure existante
    ✅ Pas de modification des méthodes existantes
    ✅ Fallbacks pour dépendances manquantes
    ✅ Gestion d'erreurs robuste
    
    6. AVANTAGES
    ------------
    
    🎯 Implémente toutes les recommandations de base.txt
    📊 Analyse entropique multi-échelle fractale
    🚨 Détection d'anomalies de fragmentation
    🔮 Prédiction adaptative Markovienne
    📋 Rapports automatiques professionnels
    🔧 Intégration transparente
    '''
    
    return instructions

if __name__ == "__main__":
    print("INTÉGRATION MODULES INDEX5 AVEC ANALYSEUR PRINCIPAL")
    print("=" * 60)
    
    print("\n1. CODE MÉTHODES À AJOUTER :")
    print("-" * 30)
    print(ajouter_methodes_index5_avancees())
    
    print("\n2. CODE MODIFICATION analyser_sequences_completes() :")
    print("-" * 50)
    print(modifier_analyser_sequences_completes())
    
    print("\n3. INSTRUCTIONS COMPLÈTES :")
    print("-" * 30)
    print(instructions_integration())
