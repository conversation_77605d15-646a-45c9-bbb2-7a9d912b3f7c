"""
INDEX5 ADVANCED ENTROPY ANALYSIS
=================================

Module spécialisé pour l'analyse entropique avancée de l'INDEX5
Implémente les recommandations méthodologiques de base.txt :
- Entropie Multi-Échelle fractale
- Modélisation Markovienne d'ordre supérieur
- Analyse spectrale des fréquences caractéristiques
- Tests de stationnarité adaptés INDEX5

Auteur : Expert Statisticien IA
Date : 2025-06-20
Version : 1.0
"""

import numpy as np
import pandas as pd
from scipy import signal, stats
from scipy.fft import fft, fftfreq
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Any
import math
import warnings

try:
    from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, diviser_en_parties
except ImportError:
    # Fallback pour compatibilité
    def nettoyer_marqueurs(sequence):
        return [x for x in sequence if x != "__FIN_PARTIE__"]

    def diviser_en_parties(sequence):
        parties = []
        partie_actuelle = []
        for element in sequence:
            if element == "__FIN_PARTIE__":
                if partie_actuelle:
                    parties.append(partie_actuelle)
                    partie_actuelle = []
            else:
                partie_actuelle.append(element)
        if partie_actuelle:
            parties.append(partie_actuelle)
        return parties


class Index5AdvancedEntropy:
    """
    Analyseur entropique avancé spécialisé pour INDEX5
    
    Implémente les 4 recommandations méthodologiques prioritaires :
    1. Entropie Multi-Échelle fractale
    2. Modélisation Markovienne d'ordre supérieur  
    3. Analyse spectrale des fréquences
    4. Tests de stationnarité
    """
    
    def __init__(self):
        """Initialise l'analyseur INDEX5 avancé"""
        self.combinaisons_index5 = [
            'SYNC_impair_5_BANKER', 'SYNC_impair_5_PLAYER', 'SYNC_impair_5_TIE',
            'SYNC_pair_4_BANKER', 'SYNC_pair_4_PLAYER', 'SYNC_pair_4_TIE',
            'SYNC_pair_6_BANKER', 'SYNC_pair_6_PLAYER', 'SYNC_pair_6_TIE',
            'DESYNC_impair_5_BANKER', 'DESYNC_impair_5_PLAYER', 'DESYNC_impair_5_TIE',
            'DESYNC_pair_4_BANKER', 'DESYNC_pair_4_PLAYER', 'DESYNC_pair_4_TIE',
            'DESYNC_pair_6_BANKER', 'DESYNC_pair_6_PLAYER', 'DESYNC_pair_6_TIE'
        ]
        self.nb_combinaisons = 18
        
    def analyser_index5_complet(self, sequence_index5: List[str]) -> Dict:
        """
        Analyse entropique complète INDEX5 selon recommandations base.txt
        
        Args:
            sequence_index5: Séquence INDEX5 brute avec marqueurs
            
        Returns:
            dict: Résultats complets des 4 analyses prioritaires
        """
        print("\n🔬 ANALYSE ENTROPIQUE AVANCÉE INDEX5 - RECOMMANDATIONS BASE.TXT")
        print("=" * 70)
        
        # Nettoyer la séquence
        sequence_propre = nettoyer_marqueurs(sequence_index5)
        
        if len(sequence_propre) < 100:
            return {'erreur': 'Séquence INDEX5 trop courte pour analyse avancée'}
        
        resultats = {}
        
        # 1. ENTROPIE MULTI-ÉCHELLE FRACTALE
        print("   📊 1. Entropie Multi-Échelle Fractale INDEX5...")
        resultats['entropie_multi_echelle'] = self._analyser_entropie_multi_echelle_index5(sequence_propre)
        
        # 2. MODÉLISATION MARKOVIENNE D'ORDRE SUPÉRIEUR
        print("   🔗 2. Modélisation Markovienne Ordre Supérieur INDEX5...")
        resultats['markov_ordre_superieur'] = self._analyser_markov_ordre_superieur_index5(sequence_propre)
        
        # 3. ANALYSE SPECTRALE FRÉQUENCES CARACTÉRISTIQUES
        print("   📈 3. Analyse Spectrale Fréquences INDEX5...")
        resultats['analyse_spectrale'] = self._analyser_spectrale_index5(sequence_propre)
        
        # 4. TESTS DE STATIONNARITÉ ADAPTÉS
        print("   📏 4. Tests Stationnarité Adaptés INDEX5...")
        resultats['tests_stationnarite'] = self._tester_stationnarite_index5(sequence_propre)
        
        # SYNTHÈSE GLOBALE
        resultats['synthese_index5'] = self._synthetiser_resultats_index5(resultats)
        
        print("   ✅ Analyse entropique avancée INDEX5 terminée")
        return resultats
    
    def _analyser_entropie_multi_echelle_index5(self, sequence: List[str]) -> Dict:
        """
        Entropie Multi-Échelle avec analyse fractale pour INDEX5
        Implémente la recommandation : "Analyser la structure fractale des transitions"
        """
        echelles = [1, 2, 3, 5, 8, 13, 21, 34, 55]  # Séquence de Fibonacci
        resultats = {
            'entropies_par_echelle': {},
            'dimension_fractale': 0.0,
            'exposant_hurst': 0.0,
            'complexite_fractale': 'FAIBLE'
        }
        
        # Encoder les combinaisons INDEX5 en nombres
        mapping = {combo: i for i, combo in enumerate(self.combinaisons_index5)}
        sequence_numerique = [mapping.get(x, 0) for x in sequence]
        
        entropies = []
        echelles_valides = []
        
        for echelle in echelles:
            if len(sequence) // echelle < 10:  # Minimum 10 points par échelle
                continue
                
            # Coarse-graining à l'échelle donnée
            sequence_coarse = self._coarse_graining_index5(sequence_numerique, echelle)
            
            # Calculer entropie de Shannon
            if len(sequence_coarse) > 0:
                entropie = self._calculer_entropie_shannon_numerique(sequence_coarse)
                entropies.append(entropie)
                echelles_valides.append(echelle)
                
                resultats['entropies_par_echelle'][echelle] = {
                    'entropie': entropie,
                    'longueur_sequence': len(sequence_coarse),
                    'nb_valeurs_uniques': len(set(sequence_coarse))
                }
        
        # Analyse fractale : régression log-log
        if len(entropies) >= 3:
            log_echelles = np.log(echelles_valides)
            log_entropies = np.log(entropies)
            
            # Régression linéaire
            slope, intercept, r_value, p_value, std_err = stats.linregress(log_echelles, log_entropies)
            
            resultats['dimension_fractale'] = -slope  # Dimension fractale
            resultats['exposant_hurst'] = 0.5 + slope  # Approximation Hurst
            resultats['correlation_fractale'] = r_value**2
            resultats['p_value_fractale'] = p_value
            
            # Classification de la complexité
            if abs(slope) < 0.1:
                resultats['complexite_fractale'] = 'UNIFORME'
            elif abs(slope) < 0.3:
                resultats['complexite_fractale'] = 'FAIBLE'
            elif abs(slope) < 0.7:
                resultats['complexite_fractale'] = 'MODÉRÉE'
            else:
                resultats['complexite_fractale'] = 'ÉLEVÉE'
        
        return resultats
    
    def _analyser_markov_ordre_superieur_index5(self, sequence: List[str]) -> Dict:
        """
        Modélisation Markovienne d'ordre supérieur pour INDEX5
        Implémente : "Capturer les dépendances cachées"
        """
        resultats = {
            'ordres_analyses': [1, 2, 3, 4],
            'matrices_transition': {},
            'entropies_conditionnelles': {},
            'ordre_optimal': 1,
            'dependances_cachees': False
        }
        
        # Encoder les combinaisons
        mapping = {combo: i for i, combo in enumerate(self.combinaisons_index5)}
        sequence_numerique = [mapping.get(x, 0) for x in sequence]
        
        entropies_ordres = {}
        
        for ordre in [1, 2, 3, 4]:
            if len(sequence_numerique) <= ordre:
                continue
                
            # Construire matrice de transition d'ordre k
            matrice, etats = self._construire_matrice_ordre_k_index5(sequence_numerique, ordre)
            
            # Calculer entropie conditionnelle
            entropie_cond = self._calculer_entropie_conditionnelle_matrice(matrice)
            
            resultats['matrices_transition'][ordre] = {
                'matrice': matrice.tolist() if hasattr(matrice, 'tolist') else matrice,
                'etats': etats,
                'taille': len(etats)
            }
            
            resultats['entropies_conditionnelles'][ordre] = entropie_cond
            entropies_ordres[ordre] = entropie_cond
        
        # Déterminer ordre optimal (minimum d'entropie conditionnelle)
        if entropies_ordres:
            ordre_optimal = min(entropies_ordres.keys(), key=lambda k: entropies_ordres[k])
            resultats['ordre_optimal'] = ordre_optimal
            
            # Détecter dépendances cachées
            if len(entropies_ordres) >= 2:
                entropie_ordre1 = entropies_ordres.get(1, float('inf'))
                entropie_ordre_opt = entropies_ordres[ordre_optimal]
                
                # Si réduction significative d'entropie avec ordre supérieur
                reduction = (entropie_ordre1 - entropie_ordre_opt) / entropie_ordre1
                resultats['reduction_entropie'] = reduction
                resultats['dependances_cachees'] = reduction > 0.05  # Seuil 5%
        
        return resultats
    
    def _analyser_spectrale_index5(self, sequence: List[str]) -> Dict:
        """
        Analyse spectrale pour identifier les fréquences caractéristiques INDEX5
        Implémente : "Identifier les fréquences caractéristiques"
        """
        resultats = {
            'frequences_dominantes': [],
            'puissances_spectrales': [],
            'periodicites_detectees': [],
            'entropie_spectrale': 0.0
        }
        
        # Encoder en signal numérique
        mapping = {combo: i for i, combo in enumerate(self.combinaisons_index5)}
        signal_numerique = np.array([mapping.get(x, 0) for x in sequence])
        
        if len(signal_numerique) < 64:  # Minimum pour FFT
            return {'erreur': 'Séquence trop courte pour analyse spectrale'}
        
        # Transformée de Fourier
        fft_values = fft(signal_numerique)
        fft_freq = fftfreq(len(signal_numerique))
        
        # Densité spectrale de puissance
        power_spectrum = np.abs(fft_values)**2
        
        # Garder seulement les fréquences positives
        positive_freq_idx = fft_freq > 0
        freq_positive = fft_freq[positive_freq_idx]
        power_positive = power_spectrum[positive_freq_idx]
        
        # Identifier pics dominants
        if len(power_positive) > 10:
            # Trouver les pics
            peaks, properties = signal.find_peaks(power_positive, 
                                                height=np.mean(power_positive) + 2*np.std(power_positive),
                                                distance=5)
            
            if len(peaks) > 0:
                # Trier par puissance décroissante
                peak_powers = power_positive[peaks]
                sorted_indices = np.argsort(peak_powers)[::-1]
                
                for i in sorted_indices[:5]:  # Top 5 pics
                    freq = freq_positive[peaks[i]]
                    power = peak_powers[i]
                    periode = 1/freq if freq > 0 else float('inf')
                    
                    resultats['frequences_dominantes'].append(freq)
                    resultats['puissances_spectrales'].append(power)
                    resultats['periodicites_detectees'].append(periode)
        
        # Entropie spectrale
        power_normalized = power_positive / np.sum(power_positive)
        power_normalized = power_normalized[power_normalized > 0]
        resultats['entropie_spectrale'] = -np.sum(power_normalized * np.log2(power_normalized))
        
        return resultats
    
    def _tester_stationnarite_index5(self, sequence: List[str]) -> Dict:
        """
        Tests de stationnarité adaptés pour INDEX5
        Implémente : "Vérifier la stabilité temporelle"
        """
        resultats = {
            'test_runs': {},
            'test_segments': {},
            'stationnarite_globale': False,
            'points_changement': []
        }
        
        # Test des runs sur la séquence complète
        resultats['test_runs'] = self._test_runs_stationnarite_index5(sequence)
        
        # Test par segments temporels
        resultats['test_segments'] = self._test_segments_stationnarite_index5(sequence)
        
        # Détection de points de changement
        resultats['points_changement'] = self._detecter_points_changement_index5(sequence)
        
        # Évaluation globale
        runs_stationnaire = resultats['test_runs'].get('p_value', 0) > 0.05
        segments_stationnaires = resultats['test_segments'].get('proportion_stationnaires', 0) > 0.7
        peu_changements = len(resultats['points_changement']) < len(sequence) // 100
        
        resultats['stationnarite_globale'] = runs_stationnaire and segments_stationnaires and peu_changements
        
        return resultats
    
    def _coarse_graining_index5(self, sequence: List[int], echelle: int) -> List[float]:
        """Coarse-graining pour entropie multi-échelle INDEX5"""
        if echelle == 1:
            return sequence
        
        coarse_sequence = []
        for i in range(0, len(sequence) - echelle + 1, echelle):
            segment = sequence[i:i+echelle]
            coarse_sequence.append(np.mean(segment))
        
        return coarse_sequence
    
    def _calculer_entropie_shannon_numerique(self, sequence: List[float]) -> float:
        """Calcule l'entropie de Shannon pour une séquence numérique"""
        if not sequence:
            return 0.0
        
        # Discrétiser si nécessaire
        if isinstance(sequence[0], float):
            # Quantification en 18 niveaux (comme INDEX5)
            sequence_discrete = np.digitize(sequence, np.linspace(min(sequence), max(sequence), 17))
        else:
            sequence_discrete = sequence
        
        # Compter les occurrences
        counts = Counter(sequence_discrete)
        total = len(sequence_discrete)
        
        # Calculer entropie
        entropie = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        
        return entropie
    
    def _construire_matrice_ordre_k_index5(self, sequence: List[int], k: int) -> Tuple[np.ndarray, List]:
        """Construit matrice de transition d'ordre k pour INDEX5"""
        if len(sequence) <= k:
            return np.array([]), []
        
        # États d'ordre k (tuples de k éléments)
        etats_k = {}
        transitions = defaultdict(lambda: defaultdict(int))
        
        # Construire les états et transitions
        for i in range(len(sequence) - k):
            etat_actuel = tuple(sequence[i:i+k])
            etat_suivant = sequence[i+k]
            
            if etat_actuel not in etats_k:
                etats_k[etat_actuel] = len(etats_k)
            
            transitions[etat_actuel][etat_suivant] += 1
        
        # Construire la matrice
        etats_liste = list(etats_k.keys())
        n_etats = len(etats_liste)
        matrice = np.zeros((n_etats, self.nb_combinaisons))
        
        for i, etat in enumerate(etats_liste):
            total_transitions = sum(transitions[etat].values())
            if total_transitions > 0:
                for etat_suivant, count in transitions[etat].items():
                    if 0 <= etat_suivant < self.nb_combinaisons:
                        matrice[i, etat_suivant] = count / total_transitions
        
        return matrice, etats_liste
    
    def _calculer_entropie_conditionnelle_matrice(self, matrice: np.ndarray) -> float:
        """Calcule l'entropie conditionnelle d'une matrice de transition"""
        if matrice.size == 0:
            return 0.0
        
        entropie_totale = 0.0
        n_etats = matrice.shape[0]
        
        for i in range(n_etats):
            row = matrice[i, :]
            row_sum = np.sum(row)
            
            if row_sum > 0:
                # Normaliser la ligne
                probs = row / row_sum
                probs = probs[probs > 0]  # Éliminer les zéros
                
                # Entropie de cette ligne
                entropie_ligne = -np.sum(probs * np.log2(probs))
                entropie_totale += entropie_ligne / n_etats
        
        return entropie_totale
    
    def _test_runs_stationnarite_index5(self, sequence: List[str]) -> Dict:
        """Test des runs pour stationnarité INDEX5"""
        # Simplification : tester la stationnarité des fréquences
        n = len(sequence)
        milieu = n // 2
        
        # Comparer distributions première/seconde moitié
        freq1 = Counter(sequence[:milieu])
        freq2 = Counter(sequence[milieu:])
        
        # Test chi-carré d'homogénéité
        combinaisons_communes = set(freq1.keys()) & set(freq2.keys())
        
        if len(combinaisons_communes) < 2:
            return {'p_value': 1.0, 'stationnaire': True}
        
        observed1 = [freq1.get(combo, 0) for combo in combinaisons_communes]
        observed2 = [freq2.get(combo, 0) for combo in combinaisons_communes]
        
        try:
            chi2, p_value = stats.chisquare(observed1, observed2)
            return {
                'chi2_statistic': chi2,
                'p_value': p_value,
                'stationnaire': p_value > 0.05
            }
        except:
            return {'p_value': 1.0, 'stationnaire': True}
    
    def _test_segments_stationnarite_index5(self, sequence: List[str], nb_segments: int = 10) -> Dict:
        """Test de stationnarité par segments INDEX5"""
        n = len(sequence)
        taille_segment = n // nb_segments
        
        if taille_segment < 10:
            return {'proportion_stationnaires': 1.0, 'nb_segments_testes': 0}
        
        segments_stationnaires = 0
        segments_testes = 0
        
        for i in range(nb_segments - 1):
            debut = i * taille_segment
            fin = (i + 1) * taille_segment
            segment1 = sequence[debut:fin]
            segment2 = sequence[fin:fin + taille_segment]
            
            if len(segment2) < taille_segment:
                break
            
            # Comparer les deux segments
            freq1 = Counter(segment1)
            freq2 = Counter(segment2)
            
            combinaisons_communes = set(freq1.keys()) & set(freq2.keys())
            
            if len(combinaisons_communes) >= 2:
                observed1 = [freq1.get(combo, 0) for combo in combinaisons_communes]
                observed2 = [freq2.get(combo, 0) for combo in combinaisons_communes]
                
                try:
                    chi2, p_value = stats.chisquare(observed1, observed2)
                    if p_value > 0.05:
                        segments_stationnaires += 1
                    segments_testes += 1
                except:
                    segments_stationnaires += 1
                    segments_testes += 1
        
        proportion = segments_stationnaires / segments_testes if segments_testes > 0 else 1.0
        
        return {
            'proportion_stationnaires': proportion,
            'nb_segments_testes': segments_testes,
            'nb_segments_stationnaires': segments_stationnaires
        }
    
    def _detecter_points_changement_index5(self, sequence: List[str]) -> List[int]:
        """Détection simple de points de changement INDEX5"""
        # Méthode basée sur la variance glissante des fréquences
        fenetre = 100
        points_changement = []
        
        if len(sequence) < 2 * fenetre:
            return points_changement
        
        variances = []
        for i in range(fenetre, len(sequence) - fenetre):
            segment = sequence[i-fenetre:i+fenetre]
            freq = Counter(segment)
            # Variance des fréquences
            freqs_values = list(freq.values())
            variance = np.var(freqs_values) if len(freqs_values) > 1 else 0
            variances.append(variance)
        
        if len(variances) > 10:
            # Détecter pics de variance
            seuil = np.mean(variances) + 2 * np.std(variances)
            for i, var in enumerate(variances):
                if var > seuil:
                    points_changement.append(i + fenetre)
        
        return points_changement
    
    def _synthetiser_resultats_index5(self, resultats: Dict) -> Dict:
        """Synthèse globale des résultats INDEX5"""
        synthese = {
            'score_complexite_globale': 0.0,
            'classification_index5': 'NORMAL',
            'recommandations': []
        }
        
        # Score de complexité (0-100)
        score = 0
        
        # Contribution entropie multi-échelle (25 points)
        if 'entropie_multi_echelle' in resultats:
            complexite = resultats['entropie_multi_echelle'].get('complexite_fractale', 'FAIBLE')
            if complexite == 'ÉLEVÉE':
                score += 25
            elif complexite == 'MODÉRÉE':
                score += 15
            elif complexite == 'FAIBLE':
                score += 5
        
        # Contribution Markov (25 points)
        if 'markov_ordre_superieur' in resultats:
            if resultats['markov_ordre_superieur'].get('dependances_cachees', False):
                score += 25
            ordre_opt = resultats['markov_ordre_superieur'].get('ordre_optimal', 1)
            score += min(ordre_opt * 5, 15)
        
        # Contribution spectrale (25 points)
        if 'analyse_spectrale' in resultats:
            nb_pics = len(resultats['analyse_spectrale'].get('frequences_dominantes', []))
            score += min(nb_pics * 5, 25)
        
        # Contribution stationnarité (25 points)
        if 'tests_stationnarite' in resultats:
            if not resultats['tests_stationnarite'].get('stationnarite_globale', True):
                score += 25  # Non-stationnarité = complexité
        
        synthese['score_complexite_globale'] = min(score, 100)
        
        # Classification
        if score < 30:
            synthese['classification_index5'] = 'SIMPLE'
        elif score < 60:
            synthese['classification_index5'] = 'NORMAL'
        elif score < 80:
            synthese['classification_index5'] = 'COMPLEXE'
        else:
            synthese['classification_index5'] = 'TRÈS_COMPLEXE'
        
        # Recommandations
        if score > 70:
            synthese['recommandations'].append("Système INDEX5 hautement complexe - Surveillance renforcée recommandée")
        if resultats.get('markov_ordre_superieur', {}).get('dependances_cachees', False):
            synthese['recommandations'].append("Dépendances cachées détectées - Modèles prédictifs avancés recommandés")
        if not resultats.get('tests_stationnarite', {}).get('stationnarite_globale', True):
            synthese['recommandations'].append("Non-stationnarité détectée - Adaptation dynamique nécessaire")
        
        return synthese


# Fonctions d'export pour compatibilité
__all__ = [
    'Index5AdvancedEntropy'
]
