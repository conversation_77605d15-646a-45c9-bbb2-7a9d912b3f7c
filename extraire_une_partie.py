"""
EXTRACTION D'UNE PARTIE POUR VÉRIFICATION MÉCANIQUE
===================================================

Extrait une partie complète du JSON pour analyser la mécanique
INDEX2 → INDEX1 découverte.

Auteur : Expert Statisticien IA
Date : 2025-06-21
Version : 1.0
"""

import json
import sys

def extraire_premiere_partie():
    """Extrait la première partie du JSON pour analyse"""
    print("📂 EXTRACTION PREMIÈRE PARTIE DU JSON")
    print("=" * 50)
    
    try:
        # Charger le dataset
        with open('dataset_baccarat_lupasco_20250617_232800.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data.get('parties') or len(data['parties']) == 0:
            print("❌ Aucune partie trouvée dans le JSON")
            return None
        
        # Prendre la première partie
        premiere_partie = data['parties'][0]
        mains = premiere_partie.get('mains', [])
        
        print(f"✅ Partie extraite : {len(mains)} mains")
        print(f"📊 Numéro partie : {premiere_partie.get('numero_partie', 'N/A')}")
        
        # Afficher les mains avec leurs indices
        print(f"\n📋 DÉTAIL DES MAINS :")
        print("-" * 80)
        print(f"{'Main':<4} {'Résultat':<8} {'INDEX1':<8} {'INDEX2':<8} {'INDEX5':<25}")
        print("-" * 80)
        
        for main in mains[:20]:  # Limiter à 20 premières mains
            numero = main.get('numero_main', 'N/A')
            resultat = main.get('resultat', 'N/A')
            index1 = main.get('index1', 'N/A')
            index2 = main.get('index2', 'N/A')
            index5 = main.get('index5_combined', 'N/A')
            
            print(f"{numero:<4} {resultat:<8} {index1:<8} {index2:<8} {index5:<25}")
        
        if len(mains) > 20:
            print(f"... et {len(mains) - 20} mains supplémentaires")
        
        return mains
        
    except Exception as e:
        print(f"❌ Erreur extraction : {e}")
        return None

def analyser_mecanique_sur_partie(mains):
    """Analyse la mécanique INDEX2 → INDEX1 sur une partie"""
    print(f"\n🔬 ANALYSE MÉCANIQUE INDEX2 → INDEX1")
    print("=" * 50)
    
    if not mains or len(mains) < 2:
        print("❌ Pas assez de mains pour analyser")
        return
    
    print(f"📊 Analyse sur {len(mains)} mains")
    print(f"\n🔍 VÉRIFICATION RÈGLE :")
    print(f"   • impair_5 → INDEX1 alterne (SYNC ↔ DESYNC)")
    print(f"   • pair_4/6 → INDEX1 perpétue (SYNC → SYNC, DESYNC → DESYNC)")
    
    print(f"\n📋 TRANSITIONS DÉTECTÉES :")
    print("-" * 70)
    print(f"{'Main':<6} {'INDEX2':<10} {'INDEX1':<8} {'→':<3} {'INDEX1+1':<8} {'Règle':<12} {'Statut':<8}")
    print("-" * 70)
    
    transitions_correctes = 0
    transitions_incorrectes = 0
    
    for i in range(len(mains) - 1):
        main_n = mains[i]
        main_n1 = mains[i + 1]
        
        numero_n = main_n.get('numero_main', 'N/A')
        index2_n = main_n.get('index2', 'N/A')
        index1_n = main_n.get('index1', 'N/A')
        index1_n1 = main_n1.get('index1', 'N/A')
        
        # Déterminer la règle attendue
        if index2_n == 'impair_5':
            regle_attendue = "ALTERNANCE"
            transition_correcte = index1_n != index1_n1
        elif index2_n in ['pair_4', 'pair_6']:
            regle_attendue = "PERPÉTUATION"
            transition_correcte = index1_n == index1_n1
        else:
            regle_attendue = "INCONNUE"
            transition_correcte = None
        
        if transition_correcte is not None:
            if transition_correcte:
                transitions_correctes += 1
                statut = "✅"
            else:
                transitions_incorrectes += 1
                statut = "❌"
        else:
            statut = "?"
        
        print(f"{numero_n:<6} {index2_n:<10} {index1_n:<8} {'→':<3} {index1_n1:<8} {regle_attendue:<12} {statut:<8}")
        
        # Limiter l'affichage
        if i >= 15:
            print(f"... et {len(mains) - 16} transitions supplémentaires")
            break
    
    # Calculer les statistiques
    total_transitions = transitions_correctes + transitions_incorrectes
    if total_transitions > 0:
        taux_reussite = (transitions_correctes / total_transitions) * 100
        
        print(f"\n📊 STATISTIQUES :")
        print("-" * 30)
        print(f"✅ Transitions correctes : {transitions_correctes}")
        print(f"❌ Transitions incorrectes : {transitions_incorrectes}")
        print(f"📈 Taux de réussite : {taux_reussite:.1f}%")
        
        if taux_reussite >= 95:
            print(f"\n🎉 MÉCANIQUE CONFIRMÉE ! ({taux_reussite:.1f}%)")
            print(f"🚨 Les patterns INDEX5 sont PRÉVISIBLES")
            print(f"❌ Exploitation impossible (pas de vraie variabilité)")
        elif taux_reussite >= 80:
            print(f"\n⚠️  Mécanique partiellement confirmée ({taux_reussite:.1f}%)")
            print(f"🔍 Analyse plus approfondie nécessaire")
        else:
            print(f"\n❌ Mécanique non confirmée ({taux_reussite:.1f}%)")
            print(f"🤔 Règle peut-être incorrecte ou incomplète")

def sauvegarder_partie_extraite(mains):
    """Sauvegarde la partie extraite pour analyse ultérieure"""
    if not mains:
        return
    
    nom_fichier = "partie_extraite_analyse.json"
    
    # Créer structure simplifiée
    partie_simplifiee = {
        "nombre_mains": len(mains),
        "mains": []
    }
    
    for main in mains:
        main_simplifiee = {
            "numero_main": main.get('numero_main'),
            "resultat": main.get('resultat'),
            "index1": main.get('index1'),
            "index2": main.get('index2'),
            "index5_combined": main.get('index5_combined')
        }
        partie_simplifiee["mains"].append(main_simplifiee)
    
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        json.dump(partie_simplifiee, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Partie sauvegardée : {nom_fichier}")

def main():
    """Analyse principale d'une partie"""
    print("🔍 EXTRACTION ET ANALYSE D'UNE PARTIE")
    print("=" * 80)
    print("Vérification de la mécanique INDEX2 → INDEX1 sur une partie complète")
    print("=" * 80)
    
    # Extraire première partie
    mains = extraire_premiere_partie()
    
    if mains:
        # Analyser la mécanique
        analyser_mecanique_sur_partie(mains)
        
        # Sauvegarder pour analyse ultérieure
        sauvegarder_partie_extraite(mains)
        
        print(f"\n" + "=" * 80)
        print("🎯 CONCLUSION")
        print("=" * 80)
        print("📋 Partie extraite et analysée")
        print("🔍 Vérifiez les résultats ci-dessus")
        print("💾 Données sauvegardées pour analyse approfondie")
        print("=" * 80)
        
        return True
    else:
        print("❌ Impossible d'extraire une partie")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
