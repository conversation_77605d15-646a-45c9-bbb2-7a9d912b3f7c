"""
Module de la classe principale refactorisée pour l'analyseur Lupasco

Ce module contient la nouvelle implémentation de AnalyseurSequencesLupasco
qui orchestre tous les modules refactorisés tout en maintenant l'interface
publique identique à l'original.

Auteur: Assistant IA
Date: 2025-06-19
"""

import os
from typing import Dict, List, Any, Optional

# Imports des modules refactorisés (sans imports circulaires)
from lupasco_refactored.core.data_loader import DataLoader
from lupasco_refactored.core.sequence_extractor import SequenceExtractor
from lupasco_refactored.statistics.basic_stats import BasicStatistics
from lupasco_refactored.statistics.transitions import TransitionAnalyzer
from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy
from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, calculer_entropie_shannon

# Imports paresseux pour éviter les imports circulaires
# Les analyseurs seront importés dynamiquement dans _initialiser_analyseurs()


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences Lupasco - Version refactorisée
    
    Cette classe maintient l'interface publique identique à l'original
    tout en utilisant l'architecture modulaire refactorisée.
    
    Interface publique maintenue :
    - __init__(fichier_json: str)
    - charger_donnees()
    - analyser_toutes_sequences()
    - generer_rapport(fichier_sortie: str = None)
    - analyser_index5_avec_formules_exactes()
    - analyser_index2_index3_avec_formules_exactes()
    - analyser_index1_index3_avec_formules_exactes()
    - analyser_transitions_desync_sync()
    - analyser_cycles_periode_2_et_3()
    - analyser_predictibilite_index3_par_index1()
    - analyser_predictibilite_index5_par_index1()
    - analyser_lupasco_par_parties(nb_parties_echantillon: int = 100)
    - analyser_runs(sequence: list, nom_sequence: str)
    - calculer_autocorrelation(sequence: list, max_lag: int = 20)
    - calculer_entropie_shannon(sequence: list)
    - analyser_statistiques_avancees(sequence: list, nom_sequence: str)
    - valider_formules_mathematiques()
    - analyser_entropie_avancee_tous_indices()
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON
        
        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0
        
        # Initialiser les modules refactorisés
        self.data_loader = None
        self.sequence_extractor = None
        self.basic_stats = BasicStatistics()
        self.transition_analyzer = TransitionAnalyzer()
        self.entropy_analyzer = AdvancedEntropy()
        self.index5_analyzer = None
        self.index2_index3_analyzer = None
        self.index1_index3_analyzer = None
        self.lupasco_analyzer = None
        self.report_generator = None
        
        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")
    
    def charger_donnees(self):
        """
        Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming
        """
        print("\n📊 Chargement des données...")
        
        try:
            # Initialiser le DataLoader
            self.data_loader = DataLoader(self.fichier_json)
            
            # Déterminer si on force le streaming (fichiers > 1 GB)
            taille_fichier = os.path.getsize(self.fichier_json)
            taille_gb = taille_fichier / (1024 * 1024 * 1024)
            force_streaming = taille_gb >= 1
            
            resultats = self.data_loader.charger_donnees(force_streaming)
            
            # Adapter les résultats pour la compatibilité
            self.sequences = resultats['sequences']
            self.nb_parties_total = resultats['nb_parties_total']
            
            # Conserver les données si disponibles (pour compatibilité)
            if 'donnees' in resultats:
                self.donnees = resultats['donnees']
            
            # Initialiser les analyseurs avec les séquences chargées
            self._initialiser_analyseurs()
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise
    
    def _initialiser_analyseurs(self):
        """Initialise tous les analyseurs avec les séquences chargées (imports paresseux)"""
        if self.sequences:
            # Imports paresseux pour éviter les imports circulaires
            from lupasco_refactored.analyzers.index5_analyzer import Index5Analyzer
            from lupasco_refactored.analyzers.index2_index3_analyzer import Index2Index3Analyzer
            from lupasco_refactored.analyzers.index1_index3_analyzer import Index1Index3Analyzer
            from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
            from lupasco_refactored.reporting.report_generator import ReportGenerator

            self.sequence_extractor = SequenceExtractor(self.sequences)
            self.index5_analyzer = Index5Analyzer(self.sequences)
            self.index2_index3_analyzer = Index2Index3Analyzer(self.sequences)
            self.index1_index3_analyzer = Index1Index3Analyzer(self.sequences)
            self.lupasco_analyzer = LupascoAnalyzer(self.sequences)
            self.report_generator = ReportGenerator(
                self.resultats,
                self.sequences,
                self.fichier_json,
                self.nb_parties_total
            )
    
    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\nLANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)
        
        # Phase 0: Validation des formules mathématiques
        print("\nPHASE 0: VALIDATION DES FORMULES MATHÉMATIQUES")
        print("-" * 55)
        self.valider_formules_mathematiques()
        
        # Phase spécialisée: Analyses avancées demandées
        print("\nPHASE SPÉCIALISÉE: ANALYSES AVANCÉES DEMANDÉES")
        print("-" * 55)
        self.analyser_transitions_desync_sync()
        self.analyser_cycles_periode_2_et_3()
        self.analyser_predictibilite_index3_par_index1()
        self.analyser_predictibilite_index5_par_index1()
        
        # Analyse Lupasco par parties avec vraies données
        self.analyser_lupasco_par_parties(nb_parties_echantillon=50)
        
        # Phase 1: Analyses INDEX5 avec formules exactes
        print("\nPHASE 1: ANALYSES INDEX5 AVEC FORMULES EXACTES")
        print("-" * 55)
        self.analyser_index5_avec_formules_exactes()
        
        # Phase 2: Analyses INDEX2_INDEX3 avec formules exactes
        print("\nPHASE 2: ANALYSES INDEX2_INDEX3 AVEC FORMULES EXACTES")
        print("-" * 55)
        self.analyser_index2_index3_avec_formules_exactes()
        
        # Phase 3: Analyses INDEX1_INDEX3 avec formules exactes
        print("\nPHASE 3: ANALYSES INDEX1_INDEX3 AVEC FORMULES EXACTES")
        print("-" * 55)
        self.analyser_index1_index3_avec_formules_exactes()
        
        # Phase 4: Analyses entropiques avancées
        print("\nPHASE 4: ANALYSES ENTROPIQUES AVANCÉES")
        print("-" * 55)
        self.analyser_entropie_avancee_tous_indices()
        
        # Phase 5: Analyses de base pour chaque séquence
        print("\nPHASE 5: ANALYSES DE BASE POUR CHAQUE SÉQUENCE")
        print("-" * 55)
        
        for nom_index in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            if nom_index in self.sequences:
                print(f"\n📊 Analyse {nom_index}")
                sequence = nettoyer_marqueurs(self.sequences[nom_index])
                
                # Analyse des runs
                resultats_runs = self.analyser_runs(sequence, nom_index)
                
                # Autocorrélation
                print(f"Calcul de l'autocorrélation...")
                autocorr = self.calculer_autocorrelation(sequence)
                
                # Entropie de Shannon
                print(f"Calcul de l'entropie de Shannon...")
                entropie = self.calculer_entropie_shannon(sequence)
                
                # Stocker les résultats
                self.resultats[nom_index] = {
                    'runs': resultats_runs,
                    'autocorrelation': autocorr,
                    'entropie_shannon': entropie,
                    'taille_sequence': len(sequence)
                }
                
                print(f"Analyse {nom_index} terminée")
        
        print("\n✅ ANALYSE COMPLÈTE TERMINÉE")
    
    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        # Import paresseux pour éviter les imports circulaires
        from lupasco_refactored.reporting.report_generator import ReportGenerator

        # Mettre à jour le générateur de rapports avec les résultats actuels
        self.report_generator = ReportGenerator(
            self.resultats,
            self.sequences,
            self.fichier_json,
            self.nb_parties_total
        )

        # Générer le rapport avec le nouveau module
        return self.report_generator.generer_rapport(fichier_sortie)
    
    # ================================================================================
    # MÉTHODES D'ANALYSE PRINCIPALES
    # ================================================================================
    
    def analyser_index5_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX5 avec toutes les formules mathématiques exactes
        pour chacune des 18 combinaisons possibles
        """
        if not self.index5_analyzer:
            raise RuntimeError("Analyseur INDEX5 non initialisé. Appelez charger_donnees() d'abord.")
        
        resultats = self.index5_analyzer.analyser_index5_avec_formules_exactes()
        
        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX5_FORMULES_EXACTES'] = resultats
        
        return resultats
    
    def analyser_index2_index3_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX2_INDEX3 avec toutes les formules mathématiques exactes
        pour chacune des 9 combinaisons possibles (INDEX2 + INDEX3)
        """
        if not self.index2_index3_analyzer:
            raise RuntimeError("Analyseur INDEX2_INDEX3 non initialisé. Appelez charger_donnees() d'abord.")
        
        resultats = self.index2_index3_analyzer.analyser_index2_index3_avec_formules_exactes()
        
        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX2_INDEX3_FORMULES_EXACTES'] = resultats
        
        return resultats
    
    def analyser_index1_index3_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX1_INDEX3 avec toutes les formules mathématiques exactes
        pour chacune des 6 combinaisons possibles (INDEX1 + INDEX3)
        """
        if not self.index1_index3_analyzer:
            raise RuntimeError("Analyseur INDEX1_INDEX3 non initialisé. Appelez charger_donnees() d'abord.")
        
        resultats = self.index1_index3_analyzer.analyser_index1_index3_avec_formules_exactes()
        
        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX1_INDEX3_FORMULES_EXACTES'] = resultats
        
        return resultats

    # ================================================================================
    # MÉTHODES LUPASCO SPÉCIALISÉES
    # ================================================================================

    def analyser_transitions_desync_sync(self):
        """
        Analyse les transitions DESYNC→SYNC et SYNC→DESYNC dans INDEX1
        """
        if not self.lupasco_analyzer:
            raise RuntimeError("Analyseur Lupasco non initialisé. Appelez charger_donnees() d'abord.")

        resultats = self.lupasco_analyzer.analyser_transitions_desync_sync()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['TRANSITIONS_DESYNC_SYNC'] = resultats

        return resultats

    def analyser_cycles_periode_2_et_3(self):
        """
        Analyse les cycles de période 2 et 3 dans toutes les séquences
        """
        if not self.lupasco_analyzer:
            raise RuntimeError("Analyseur Lupasco non initialisé. Appelez charger_donnees() d'abord.")

        resultats = self.lupasco_analyzer.analyser_cycles_periode_2_et_3()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['CYCLES_PERIODE_2_ET_3'] = resultats

        return resultats

    def analyser_predictibilite_index3_par_index1(self):
        """
        Analyse la prédictibilité d'INDEX3 par INDEX1
        """
        if not self.lupasco_analyzer:
            raise RuntimeError("Analyseur Lupasco non initialisé. Appelez charger_donnees() d'abord.")

        resultats = self.lupasco_analyzer.analyser_predictibilite_index3_par_index1()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['PREDICTIBILITE_INDEX3_PAR_INDEX1'] = resultats

        return resultats

    def analyser_predictibilite_index5_par_index1(self):
        """
        Analyse la prédictibilité d'INDEX5 par INDEX1
        (utilise INDEX3 comme proxy pour INDEX5)
        """
        if not self.lupasco_analyzer:
            raise RuntimeError("Analyseur Lupasco non initialisé. Appelez charger_donnees() d'abord.")

        # Utiliser INDEX3 comme proxy pour INDEX5 (même logique)
        resultats = self.lupasco_analyzer.analyser_predictibilite_index3_par_index1()

        # Adapter les résultats pour INDEX5
        if 'erreur' not in resultats:
            resultats['note'] = 'Analyse INDEX5 par INDEX1 utilise la même logique que INDEX3 par INDEX1'

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['PREDICTIBILITE_INDEX5_PAR_INDEX1'] = resultats

        return resultats

    def analyser_lupasco_par_parties(self, nb_parties_echantillon: int = 100):
        """
        Analyse Lupasco sur un échantillon de parties indépendantes

        Args:
            nb_parties_echantillon: Nombre de parties à analyser
        """
        if not self.lupasco_analyzer:
            raise RuntimeError("Analyseur Lupasco non initialisé. Appelez charger_donnees() d'abord.")

        resultats = self.lupasco_analyzer.analyser_lupasco_par_parties(nb_parties_echantillon)

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['LUPASCO_PAR_PARTIES'] = resultats

        return resultats

    # ================================================================================
    # MÉTHODES STATISTIQUES DE BASE
    # ================================================================================

    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence
        Respecte l'indépendance des parties

        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage

        Returns:
            dict: Résultats de l'analyse des runs
        """
        return self.basic_stats.analyser_runs(sequence, nom_sequence)

    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence

        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer

        Returns:
            dict: Coefficients d'autocorrélation
        """
        return self.basic_stats.calculer_autocorrelation(sequence, max_lag)

    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence

        Args:
            sequence: Séquence à analyser

        Returns:
            float: Entropie de Shannon
        """
        return calculer_entropie_shannon(sequence)

    def analyser_statistiques_avancees(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse statistique complète avec les nouvelles méthodes validées

        Args:
            sequence: Séquence à analyser
            nom_sequence: Nom de la séquence

        Returns:
            dict: Résultats des analyses avancées
        """
        # Cette méthode était complexe dans l'original, nous la simplifions
        # en utilisant les modules refactorisés
        resultats = {
            'runs': self.analyser_runs(sequence, nom_sequence),
            'autocorrelation': self.calculer_autocorrelation(sequence),
            'entropie_shannon': self.calculer_entropie_shannon(sequence),
            'taille_sequence': len(sequence)
        }

        return resultats

    # ================================================================================
    # MÉTHODES DE VALIDATION ET ENTROPIE AVANCÉE
    # ================================================================================

    def valider_formules_mathematiques(self):
        """
        Valide toutes les formules mathématiques utilisées
        """
        try:
            # Importer le module de validation
            from lupasco_refactored.utils.math_formulas import MathFormulas

            validator = MathFormulas()
            resultats = validator.valider_toutes_formules()

            # Stocker les résultats dans self.resultats pour compatibilité
            self.resultats['VALIDATION_FORMULES'] = resultats

            return resultats

        except ImportError:
            # Fallback si le module n'est pas disponible
            print("⚠️ Module de validation des formules non disponible")
            resultats = {
                'nb_tests_reussis': 0,
                'nb_tests_total': 0,
                'toutes_validees': False,
                'erreur': 'Module de validation non disponible'
            }
            self.resultats['VALIDATION_FORMULES'] = resultats
            return resultats

    def analyser_entropie_avancee_tous_indices(self):
        """
        Analyse entropique avancée complète pour tous les indices
        """
        if not self.entropy_analyzer:
            raise RuntimeError("Analyseur d'entropie non initialisé.")

        resultats = self.entropy_analyzer.analyser_entropie_avancee_complete(self.sequences)

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['ENTROPIE_AVANCEE'] = resultats

        return resultats

    # ================================================================================
    # MÉTHODES PRIVÉES POUR COMPATIBILITÉ
    # ================================================================================

    def _charger_avec_streaming(self):
        """
        Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)
        """
        if not self.data_loader:
            self.data_loader = DataLoader(self.fichier_json)

        resultats = self.data_loader._charger_avec_streaming()

        # Adapter les résultats pour la compatibilité
        self.sequences = resultats['sequences']
        self.nb_parties_total = resultats['nb_parties_total']

    def _charger_standard(self):
        """
        Chargement standard pour fichiers plus petits
        """
        if not self.data_loader:
            self.data_loader = DataLoader(self.fichier_json)

        resultats = self.data_loader._charger_standard()

        # Adapter les résultats pour la compatibilité
        self.sequences = resultats['sequences']
        self.nb_parties_total = resultats['nb_parties_total']

        # Conserver les données si disponibles (pour compatibilité)
        if 'donnees' in resultats:
            self.donnees = resultats['donnees']

    def _extraire_sequences(self):
        """
        Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties
        """
        if not self.data_loader:
            self.data_loader = DataLoader(self.fichier_json)

        self.data_loader.donnees = self.donnees  # Passer les données déjà chargées
        total_mains = self.data_loader._extraire_sequences()

        # Adapter les résultats pour la compatibilité
        self.sequences = self.data_loader.sequences

        return total_mains

    def _nettoyer_marqueurs(self, sequence):
        """
        Supprime les marqueurs __FIN_PARTIE__ d'une séquence
        """
        return nettoyer_marqueurs(sequence)

    def _extraire_composants_index5(self):
        """
        Extrait INDEX1, INDEX2, INDEX3 depuis self.sequences['INDEX5']
        Format: "SYNC_pair_4_BANKER" → ["SYNC", "pair_4", "BANKER"]
        """
        if not self.sequence_extractor:
            self.sequence_extractor = SequenceExtractor(self.sequences)

        return self.sequence_extractor.extraire_composants_index5()

    def _extraire_composants_index2_index3(self):
        """
        Extrait INDEX2, INDEX3 depuis self.sequences['INDEX2_INDEX3']
        Format: "pair_4_BANKER" → ["pair_4", "BANKER"]
        """
        if not self.sequence_extractor:
            self.sequence_extractor = SequenceExtractor(self.sequences)

        return self.sequence_extractor.extraire_composants_index2_index3()

    def _extraire_composants_index1_index3(self):
        """
        Extrait INDEX1, INDEX3 depuis self.sequences['INDEX1_INDEX3']
        Format: "SYNC_BANKER" → ["SYNC", "BANKER"]
        """
        if not self.sequence_extractor:
            self.sequence_extractor = SequenceExtractor(self.sequences)

        return self.sequence_extractor.extraire_composants_index1_index3()

    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """
        Analyse les runs pour une valeur spécifique
        """
        return self.basic_stats.analyser_runs_valeur(longueurs, valeur, n_total, n_valeur)

    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """
        Analyse globale des runs
        """
        return self.basic_stats.analyser_runs_global(runs, sequence)
